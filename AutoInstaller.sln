Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1

# Core Projects - Clean Architecture Foundation
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Core", "src\AutoInstaller.Core\AutoInstaller.Core.csproj", "{A1B2C3D4-E5F6-7890-ABCD-123456789ABC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Application", "src\AutoInstaller.Application\AutoInstaller.Application.csproj", "{B2C3D4E5-F6G7-8901-BCDE-23456789ABCD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Infrastructure", "src\AutoInstaller.Infrastructure\AutoInstaller.Infrastructure.csproj", "{C3D4E5F6-G7H8-9012-CDEF-3456789ABCDE}"
EndProject

# UI Layer - Avalonia Docker Desktop Theme
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.UI", "src\AutoInstaller.UI\AutoInstaller.UI.csproj", "{D4E5F6G7-H8I9-0123-DEFG-456789ABCDEF}"
EndProject

# Engine Management - Embedded Engines System
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.EngineManager", "src\AutoInstaller.EngineManager\AutoInstaller.EngineManager.csproj", "{E5F6G7H8-I9J0-1234-EFGH-56789ABCDEFG}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Docker", "src\AutoInstaller.Docker\AutoInstaller.Docker.csproj", "{F6G7H8I9-J0K1-2345-FGHI-6789ABCDEFGH}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Podman", "src\AutoInstaller.Podman\AutoInstaller.Podman.csproj", "{G7H8I9J0-K1L2-3456-GHIJ-789ABCDEFGHI}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.FallbackSystem", "src\AutoInstaller.FallbackSystem\AutoInstaller.FallbackSystem.csproj", "{H8I9J0K1-L2M3-4567-HIJK-89ABCDEFGHIJ}"
EndProject

# Test Projects - Quality Assurance >80% Coverage
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Tests.Unit", "tests\AutoInstaller.Tests.Unit\AutoInstaller.Tests.Unit.csproj", "{I9J0K1L2-M3N4-5678-IJKL-9ABCDEFGHIJK}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.Tests.Integration", "tests\AutoInstaller.Tests.Integration\AutoInstaller.Tests.Integration.csproj", "{J0K1L2M3-N4O5-6789-JKLM-ABCDEFGHIJKL}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AutoInstaller.UI.Tests", "tests\AutoInstaller.UI.Tests\AutoInstaller.UI.Tests.csproj", "{K1L2M3N4-O5P6-7890-KLMN-BCDEFGHIJKLM}"
EndProject

# Solution Folders for Organization
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{00000000-0000-0000-0000-000000000001}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{00000000-0000-0000-0000-000000000002}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "engines", "engines", "{00000000-0000-0000-0000-000000000003}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "build", "build", "{00000000-0000-0000-0000-000000000004}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{00000000-0000-0000-0000-000000000005}"
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-123456789ABC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-123456789ABC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-123456789ABC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-123456789ABC}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-123456789ABC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-123456789ABC}.Debug|x64.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-123456789ABC}.Release|x64.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-123456789ABC}.Release|x64.Build.0 = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-23456789ABCD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-23456789ABCD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-23456789ABCD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-23456789ABCD}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-23456789ABCD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-23456789ABCD}.Debug|x64.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-23456789ABCD}.Release|x64.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-23456789ABCD}.Release|x64.Build.0 = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-3456789ABCDE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-3456789ABCDE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-3456789ABCDE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-3456789ABCDE}.Release|x64.Build.0 = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-3456789ABCDE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-3456789ABCDE}.Debug|x64.Build.0 = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-3456789ABCDE}.Release|x64.ActiveCfg = Release|Any CPU
		{D4E5F6G7-H8I9-0123-DEFG-456789ABCDEF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4E5F6G7-H8I9-0123-DEFG-456789ABCDEF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4E5F6G7-H8I9-0123-DEFG-456789ABCDEF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4E5F6G7-H8I9-0123-DEFG-456789ABCDEF}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4E5F6G7-H8I9-0123-DEFG-456789ABCDEF}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D4E5F6G7-H8I9-0123-DEFG-456789ABCDEF}.Debug|x64.Build.0 = Debug|Any CPU
		{D4E5F6G7-H8I9-0123-DEFG-456789ABCDEF}.Release|x64.ActiveCfg = Release|Any CPU
		{D4E5F6G7-H8I9-0123-DEFG-456789ABCDEF}.Release|x64.Build.0 = Release|Any CPU
		{E5F6G7H8-I9J0-1234-EFGH-56789ABCDEFG}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5F6G7H8-I9J0-1234-EFGH-56789ABCDEFG}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5F6G7H8-I9J0-1234-EFGH-56789ABCDEFG}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5F6G7H8-I9J0-1234-EFGH-56789ABCDEFG}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5F6G7H8-I9J0-1234-EFGH-56789ABCDEFG}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E5F6G7H8-I9J0-1234-EFGH-56789ABCDEFG}.Debug|x64.Build.0 = Debug|Any CPU
		{E5F6G7H8-I9J0-1234-EFGH-56789ABCDEFG}.Release|x64.ActiveCfg = Release|Any CPU
		{E5F6G7H8-I9J0-1234-EFGH-56789ABCDEFG}.Release|x64.Build.0 = Release|Any CPU
		{F6G7H8I9-J0K1-2345-FGHI-6789ABCDEFGH}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6G7H8I9-J0K1-2345-FGHI-6789ABCDEFGH}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6G7H8I9-J0K1-2345-FGHI-6789ABCDEFGH}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6G7H8I9-J0K1-2345-FGHI-6789ABCDEFGH}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6G7H8I9-J0K1-2345-FGHI-6789ABCDEFGH}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F6G7H8I9-J0K1-2345-FGHI-6789ABCDEFGH}.Debug|x64.Build.0 = Debug|Any CPU
		{F6G7H8I9-J0K1-2345-FGHI-6789ABCDEFGH}.Release|x64.ActiveCfg = Release|Any CPU
		{F6G7H8I9-J0K1-2345-FGHI-6789ABCDEFGH}.Release|x64.Build.0 = Release|Any CPU
		{G7H8I9J0-K1L2-3456-GHIJ-789ABCDEFGHI}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{G7H8I9J0-K1L2-3456-GHIJ-789ABCDEFGHI}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{G7H8I9J0-K1L2-3456-GHIJ-789ABCDEFGHI}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{G7H8I9J0-K1L2-3456-GHIJ-789ABCDEFGHI}.Release|Any CPU.Build.0 = Release|Any CPU
		{G7H8I9J0-K1L2-3456-GHIJ-789ABCDEFGHI}.Debug|x64.ActiveCfg = Debug|Any CPU
		{G7H8I9J0-K1L2-3456-GHIJ-789ABCDEFGHI}.Debug|x64.Build.0 = Debug|Any CPU
		{G7H8I9J0-K1L2-3456-GHIJ-789ABCDEFGHI}.Release|x64.ActiveCfg = Release|Any CPU
		{G7H8I9J0-K1L2-3456-GHIJ-789ABCDEFGHI}.Release|x64.Build.0 = Release|Any CPU
		{H8I9J0K1-L2M3-4567-HIJK-89ABCDEFGHIJ}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{H8I9J0K1-L2M3-4567-HIJK-89ABCDEFGHIJ}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{H8I9J0K1-L2M3-4567-HIJK-89ABCDEFGHIJ}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{H8I9J0K1-L2M3-4567-HIJK-89ABCDEFGHIJ}.Release|Any CPU.Build.0 = Release|Any CPU
		{H8I9J0K1-L2M3-4567-HIJK-89ABCDEFGHIJ}.Debug|x64.ActiveCfg = Debug|Any CPU
		{H8I9J0K1-L2M3-4567-HIJK-89ABCDEFGHIJ}.Debug|x64.Build.0 = Debug|Any CPU
		{H8I9J0K1-L2M3-4567-HIJK-89ABCDEFGHIJ}.Release|x64.ActiveCfg = Release|Any CPU
		{H8I9J0K1-L2M3-4567-HIJK-89ABCDEFGHIJ}.Release|x64.Build.0 = Release|Any CPU
		{I9J0K1L2-M3N4-5678-IJKL-9ABCDEFGHIJK}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{I9J0K1L2-M3N4-5678-IJKL-9ABCDEFGHIJK}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{I9J0K1L2-M3N4-5678-IJKL-9ABCDEFGHIJK}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{I9J0K1L2-M3N4-5678-IJKL-9ABCDEFGHIJK}.Release|Any CPU.Build.0 = Release|Any CPU
		{I9J0K1L2-M3N4-5678-IJKL-9ABCDEFGHIJK}.Debug|x64.ActiveCfg = Debug|Any CPU
		{I9J0K1L2-M3N4-5678-IJKL-9ABCDEFGHIJK}.Debug|x64.Build.0 = Debug|Any CPU
		{I9J0K1L2-M3N4-5678-IJKL-9ABCDEFGHIJK}.Release|x64.ActiveCfg = Release|Any CPU
		{I9J0K1L2-M3N4-5678-IJKL-9ABCDEFGHIJK}.Release|x64.Build.0 = Release|Any CPU
		{J0K1L2M3-N4O5-6789-JKLM-ABCDEFGHIJKL}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{J0K1L2M3-N4O5-6789-JKLM-ABCDEFGHIJKL}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{J0K1L2M3-N4O5-6789-JKLM-ABCDEFGHIJKL}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{J0K1L2M3-N4O5-6789-JKLM-ABCDEFGHIJKL}.Release|Any CPU.Build.0 = Release|Any CPU
		{J0K1L2M3-N4O5-6789-JKLM-ABCDEFGHIJKL}.Debug|x64.ActiveCfg = Debug|Any CPU
		{J0K1L2M3-N4O5-6789-JKLM-ABCDEFGHIJKL}.Debug|x64.Build.0 = Debug|Any CPU
		{J0K1L2M3-N4O5-6789-JKLM-ABCDEFGHIJKL}.Release|x64.ActiveCfg = Release|Any CPU
		{J0K1L2M3-N4O5-6789-JKLM-ABCDEFGHIJKL}.Release|x64.Build.0 = Release|Any CPU
		{K1L2M3N4-O5P6-7890-KLMN-BCDEFGHIJKLM}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{K1L2M3N4-O5P6-7890-KLMN-BCDEFGHIJKLM}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{K1L2M3N4-O5P6-7890-KLMN-BCDEFGHIJKLM}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{K1L2M3N4-O5P6-7890-KLMN-BCDEFGHIJKLM}.Release|Any CPU.Build.0 = Release|Any CPU
		{K1L2M3N4-O5P6-7890-KLMN-BCDEFGHIJKLM}.Debug|x64.ActiveCfg = Debug|Any CPU
		{K1L2M3N4-O5P6-7890-KLMN-BCDEFGHIJKLM}.Debug|x64.Build.0 = Debug|Any CPU
		{K1L2M3N4-O5P6-7890-KLMN-BCDEFGHIJKLM}.Release|x64.ActiveCfg = Release|Any CPU
		{K1L2M3N4-O5P6-7890-KLMN-BCDEFGHIJKLM}.Release|x64.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A1B2C3D4-E5F6-7890-ABCD-123456789ABC} = {00000000-0000-0000-0000-000000000001}
		{B2C3D4E5-F6G7-8901-BCDE-23456789ABCD} = {00000000-0000-0000-0000-000000000001}
		{C3D4E5F6-G7H8-9012-CDEF-3456789ABCDE} = {00000000-0000-0000-0000-000000000001}
		{D4E5F6G7-H8I9-0123-DEFG-456789ABCDEF} = {00000000-0000-0000-0000-000000000001}
		{E5F6G7H8-I9J0-1234-EFGH-56789ABCDEFG} = {00000000-0000-0000-0000-000000000001}
		{F6G7H8I9-J0K1-2345-FGHI-6789ABCDEFGH} = {00000000-0000-0000-0000-000000000001}
		{G7H8I9J0-K1L2-3456-GHIJ-789ABCDEFGHI} = {00000000-0000-0000-0000-000000000001}
		{H8I9J0K1-L2M3-4567-HIJK-89ABCDEFGHIJ} = {00000000-0000-0000-0000-000000000001}
		{I9J0K1L2-M3N4-5678-IJKL-9ABCDEFGHIJK} = {00000000-0000-0000-0000-000000000002}
		{J0K1L2M3-N4O5-6789-JKLM-ABCDEFGHIJKL} = {00000000-0000-0000-0000-000000000002}
		{K1L2M3N4-O5P6-7890-KLMN-BCDEFGHIJKLM} = {00000000-0000-0000-0000-000000000002}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789ABC}
	EndGlobalSection
EndGlobal
