# Diretrizes de Desenvolvimento - Auto-Instalador Desktop Multiplataforma Autônomo

## Índice
1. [Princípios Fundamentais do Sistema Autônomo](#princípios-fundamentais-do-sistema-autônomo)
2. [Padrões para Engines Embarcadas](#padrões-para-engines-embarcadas)
3. [Sistema de 7 Agentes Especializados](#sistema-de-7-agentes-especializados)
4. [Arquitetura e Design Autônomo](#arquitetura-e-design-autônomo)
5. [Controle de Versão](#controle-de-versão)
6. [Testes com Engines Embarcadas](#testes-com-engines-embarcadas)
7. [Documentação dos Agentes](#documentação-dos-agentes)
8. [Performance com Engines Locais](#performance-com-engines-locais)
9. [Segurança de Engines Embarcadas](#segurança-de-engines-embarcadas)
10. [Processo de Desenvolvimento Coordenado](#processo-de-desenvolvimento-coordenado)
11. [Code Review por Agentes](#code-review-por-agentes)

---

## Princípios Fundamentais do Sistema Autônomo

### 1. Clean Code para Sistema Autônomo
- **Legibilidade**: O código deve ser autoexplicativo, especialmente para engines embarcadas
- **Simplicidade**: Prefira soluções simples para gerenciamento de processos locais
- **Responsabilidade Única**: Cada agente tem uma especialização específica
- **DRY (Don't Repeat Yourself)**: Evite duplicação entre agentes Docker e Podman
- **KISS (Keep It Simple, Stupid)**: Mantenha configurações de engines simples

### 2. SOLID Principles Aplicados aos Agentes
- **S** - Single Responsibility: Cada agente tem uma especialização única
- **O** - Open/Closed: Agentes extensíveis para novas engines
- **L** - Liskov Substitution: Engines Docker/Podman intercambiáveis
- **I** - Interface Segregation: Interfaces específicas por tipo de engine
- **D** - Dependency Inversion: Abstrações para engines embarcadas

### 3. Princípios Específicos do Sistema Autônomo
- **Engine-Embedded**: Sempre use engines embarcadas como primeira opção
- **API-Only**: NUNCA use CLI - apenas Docker.DotNet e PodManClient.DotNet
- **Fallback-Aware**: Implemente detecção e fallback inteligente
- **Agent-Coordinated**: Coordene através do sistema de 7 agentes
- **Docker-Desktop-UI**: Interface rigorosamente no estilo Docker Desktop
- **Autonomous-First**: Sistema deve funcionar sem dependências externas
- **Process-Managed**: Gerencie engines como processos locais controlados

### 4. Protocolo de Comunicação dos Agentes
- **Feedback Obrigatório**: Todo agente deve reportar status ao Gerente
- **Formato Padronizado**: Use templates de comunicação estabelecidos
- **Delegação Clara**: Gerente nunca executa tarefas técnicas
- **Especialização Rigorosa**: Agentes só trabalham em sua área
- **Colaboração Coordenada**: Comunicação entre agentes via Gerente

---

## Padrões de Código

### Convenções de Nomenclatura

#### Classes e Interfaces
```csharp
// ✅ Correto
public class ContainerService { }
public interface IContainerRepository { }
public abstract class BaseViewModel { }

// ❌ Incorreto
public class containerService { }
public interface ContainerRepository { }
public class container_service { }
```

#### Métodos e Propriedades
```csharp
// ✅ Correto
public async Task<Container> GetContainerByIdAsync(Guid id)
public string ContainerName { get; set; }
public bool IsRunning { get; private set; }

// ❌ Incorreto
public async Task<Container> getContainer(Guid id)
public string containerName { get; set; }
public bool is_running { get; set; }
```

#### Variáveis e Parâmetros
```csharp
// ✅ Correto
var containerList = new List<Container>();
string containerName = "my-container";
int maxRetryCount = 3;

// ❌ Incorreto
var ContainerList = new List<Container>();
string container_name = "my-container";
int MaxRetryCount = 3;
```

#### Constantes e Enums
```csharp
// ✅ Correto
public const string DEFAULT_DOCKER_HOST = "npipe://./pipe/docker_engine";
public enum ContainerStatus { Running, Stopped, Paused }

// ❌ Incorreto
public const string default_docker_host = "npipe://./pipe/docker_engine";
public enum containerStatus { running, stopped, paused }
```

### Estrutura de Arquivos

```
src/
├── AutoInstaller.Core/                 # Domain Layer
│   ├── Entities/
│   ├── ValueObjects/
│   ├── Repositories/
│   ├── Services/
│   └── Events/
├── AutoInstaller.Application/          # Application Layer
│   ├── Commands/
│   ├── Queries/
│   ├── Handlers/
│   ├── DTOs/
│   ├── Validators/
│   └── Services/
├── AutoInstaller.Infrastructure/       # Infrastructure Layer
│   ├── Data/
│   ├── Repositories/
│   ├── Services/
│   └── External/
├── AutoInstaller.UI/                   # Presentation Layer
│   ├── Views/
│   ├── ViewModels/
│   ├── Controls/
│   ├── Converters/
│   ├── Services/
│   └── Styles/
└── AutoInstaller.Shared/               # Shared Layer
    ├── Constants/
    ├── Enums/
    ├── Extensions/
    └── Utilities/
```

### Formatação de Código

#### EditorConfig (.editorconfig)
```ini
root = true

[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
trim_trailing_whitespace = true

[*.{cs,csx,vb,vbx}]
indent_style = space
indent_size = 4

[*.{json,yml,yaml}]
indent_style = space
indent_size = 2

[*.{xml,axaml,xaml}]
indent_style = space
indent_size = 2

# C# formatting rules
[*.cs]
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types = true
csharp_new_line_between_query_expression_clauses = true

csharp_indent_case_contents = true
csharp_indent_switch_labels = true
csharp_indent_labels = flush_left

csharp_space_after_cast = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_around_binary_operators = before_and_after
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis = false
csharp_space_between_method_call_empty_parameter_list_parentheses = false
```

---

## Arquitetura e Design

### Clean Architecture

#### Dependências
```
UI → Application → Domain
     ↓
Infrastructure → Domain
```

#### Regras de Dependência
1. **Domain** não depende de nada
2. **Application** depende apenas do Domain
3. **Infrastructure** depende do Domain
4. **UI** depende do Application e pode usar Infrastructure via DI

### Padrões de Design Obrigatórios

#### 1. Repository Pattern
```csharp
// Interface no Domain
public interface IContainerRepository
{
    Task<Container?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<Container>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<Container> AddAsync(Container container, CancellationToken cancellationToken = default);
    Task<Container> UpdateAsync(Container container, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}

// Implementação na Infrastructure
public class ContainerRepository : BaseRepository<Container>, IContainerRepository
{
    public ContainerRepository(ApplicationDbContext context, ILogger<ContainerRepository> logger) 
        : base(context, logger) { }
    
    // Implementações específicas...
}
```

#### 2. CQRS com MediatR
```csharp
// Command
public record CreateContainerCommand : IRequest<Result<ContainerDto>>
{
    public string Name { get; init; } = string.Empty;
    public string Image { get; init; } = string.Empty;
    public Dictionary<string, string> Environment { get; init; } = new();
}

// Handler
public class CreateContainerHandler : IRequestHandler<CreateContainerCommand, Result<ContainerDto>>
{
    private readonly IContainerRepository _repository;
    private readonly IDockerService _dockerService;
    
    public CreateContainerHandler(IContainerRepository repository, IDockerService dockerService)
    {
        _repository = repository;
        _dockerService = dockerService;
    }
    
    public async Task<Result<ContainerDto>> Handle(CreateContainerCommand request, CancellationToken cancellationToken)
    {
        // Implementação...
    }
}
```

#### 3. Factory Pattern para Clients
```csharp
public interface IContainerClientFactory
{
    IContainerClient CreateDockerClient();
    IContainerClient CreatePodmanClient();
    IContainerClient CreateClient(ContainerEngine engine);
}

public class ContainerClientFactory : IContainerClientFactory
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ContainerClientFactory> _logger;
    
    public ContainerClientFactory(IConfiguration configuration, ILogger<ContainerClientFactory> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }
    
    public IContainerClient CreateDockerClient()
    {
        var config = new DockerClientConfiguration(new Uri(_configuration["Docker:Host"]));
        return new DockerContainerClient(config.CreateClient(), _logger);
    }
    

    
    public IContainerClient CreateClient(ContainerEngine engine)
    {
        return engine switch
        {
            ContainerEngine.Docker => CreateDockerClient(),
            ContainerEngine.Podman => CreatePodmanClient(),
            _ => throw new NotSupportedException($"Container engine {engine} not supported")
        };
    }
}
```

### Injeção de Dependência

#### Configuração de Serviços
```csharp
// Program.cs ou ServiceCollectionExtensions
public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // MediatR
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(CreateContainerCommand).Assembly));
        
        // Validators
        services.AddValidatorsFromAssembly(typeof(CreateContainerCommandValidator).Assembly);
        
        // Application Services
        services.AddScoped<IContainerService, ContainerService>();
        services.AddScoped<IImageService, ImageService>();
        
        return services;
    }
    
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Database
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseSqlite(configuration.GetConnectionString("DefaultConnection")));
        
        // Repositories
        services.AddScoped<IContainerRepository, ContainerRepository>();
        services.AddScoped<IImageRepository, ImageRepository>();
        
        // External Services
        services.AddSingleton<IContainerClientFactory, ContainerClientFactory>();
        services.AddScoped<IDockerService, DockerService>();
        
        return services;
    }
    
    public static IServiceCollection AddUIServices(this IServiceCollection services)
    {
        // ViewModels
        services.AddTransient<MainWindowViewModel>();
        services.AddTransient<ContainerListViewModel>();
        services.AddTransient<ImageListViewModel>();
        
        // UI Services
        services.AddSingleton<INavigationService, NavigationService>();
        services.AddSingleton<IDialogService, DialogService>();
        services.AddSingleton<INotificationService, NotificationService>();
        
        return services;
    }
}
```

---

## Controle de Versão

### Git Workflow

#### Branch Strategy (Git Flow)
```
main
├── develop
│   ├── feature/container-management
│   ├── feature/image-management
│   └── feature/ui-improvements
├── release/v1.0.0
└── hotfix/critical-bug-fix
```

#### Convenções de Commit
```
type(scope): description

[optional body]

[optional footer]
```

**Tipos de Commit:**
- `feat`: Nova funcionalidade
- `fix`: Correção de bug
- `docs`: Documentação
- `style`: Formatação de código
- `refactor`: Refatoração
- `test`: Testes
- `chore`: Tarefas de manutenção

**Exemplos:**
```
feat(containers): add container creation functionality
fix(docker): resolve connection timeout issue
docs(readme): update installation instructions
refactor(ui): improve navigation service architecture
test(containers): add unit tests for container service
```

#### Pre-commit Hooks
```yaml
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: dotnet-format
        name: dotnet format
        entry: dotnet format --verify-no-changes
        language: system
        types: [c#]
        
      - id: dotnet-test
        name: dotnet test
        entry: dotnet test
        language: system
        pass_filenames: false
        
      - id: conventional-commits
        name: Conventional Commits
        entry: conventional-pre-commit
        language: python
        stages: [commit-msg]
```

---

## Testes

### Estratégia de Testes

#### Pirâmide de Testes
```
    E2E Tests (10%)
   ________________
  Integration Tests (20%)
 ________________________
Unit Tests (70%)
```

#### Estrutura de Testes
```
tests/
├── AutoInstaller.Core.Tests/           # Unit Tests - Domain
├── AutoInstaller.Application.Tests/    # Unit Tests - Application
├── AutoInstaller.Infrastructure.Tests/ # Integration Tests
├── AutoInstaller.UI.Tests/            # Unit Tests - UI
└── AutoInstaller.E2E.Tests/           # End-to-End Tests
```

### Padrões de Testes

#### Unit Tests
```csharp
[Fact]
public async Task CreateContainer_WithValidData_ShouldReturnSuccess()
{
    // Arrange
    var repository = new Mock<IContainerRepository>();
    var dockerService = new Mock<IDockerService>();
    var logger = new Mock<ILogger<CreateContainerHandler>>();
    
    var handler = new CreateContainerHandler(repository.Object, dockerService.Object, logger.Object);
    var command = new CreateContainerCommand
    {
        Name = "test-container",
        Image = "nginx:latest"
    };
    
    var expectedContainer = new Container(command.Name, command.Image);
    repository.Setup(x => x.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()))
             .ReturnsAsync(expectedContainer);
    
    dockerService.Setup(x => x.CreateContainerAsync(It.IsAny<CreateContainerRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Success("container-id"));
    
    // Act
    var result = await handler.Handle(command, CancellationToken.None);
    
    // Assert
    result.IsSuccess.Should().BeTrue();
    result.Value.Name.Should().Be(command.Name);
    result.Value.Image.Should().Be(command.Image);
    
    repository.Verify(x => x.AddAsync(It.IsAny<Container>(), It.IsAny<CancellationToken>()), Times.Once);
    dockerService.Verify(x => x.CreateContainerAsync(It.IsAny<CreateContainerRequest>(), It.IsAny<CancellationToken>()), Times.Once);
}

[Theory]
[InlineData("")]
[InlineData(null)]
[InlineData("   ")]
public async Task CreateContainer_WithInvalidName_ShouldReturnFailure(string invalidName)
{
    // Arrange
    var repository = new Mock<IContainerRepository>();
    var dockerService = new Mock<IDockerService>();
    var logger = new Mock<ILogger<CreateContainerHandler>>();
    
    var handler = new CreateContainerHandler(repository.Object, dockerService.Object, logger.Object);
    var command = new CreateContainerCommand
    {
        Name = invalidName,
        Image = "nginx:latest"
    };
    
    // Act
    var result = await handler.Handle(command, CancellationToken.None);
    
    // Assert
    result.IsSuccess.Should().BeFalse();
    result.Error.Should().Contain("Name");
}
```

#### Integration Tests
```csharp
public class ContainerRepositoryIntegrationTests : IClassFixture<DatabaseFixture>
{
    private readonly DatabaseFixture _fixture;
    
    public ContainerRepositoryIntegrationTests(DatabaseFixture fixture)
    {
        _fixture = fixture;
    }
    
    [Fact]
    public async Task AddAsync_WithValidContainer_ShouldPersistToDatabase()
    {
        // Arrange
        using var context = _fixture.CreateContext();
        var logger = new Mock<ILogger<ContainerRepository>>();
        var repository = new ContainerRepository(context, logger.Object);
        
        var container = new Container("test-container", "nginx:latest");
        
        // Act
        var result = await repository.AddAsync(container);
        
        // Assert
        result.Should().NotBeNull();
        result.Id.Should().NotBe(Guid.Empty);
        
        var savedContainer = await context.Containers.FindAsync(result.Id);
        savedContainer.Should().NotBeNull();
        savedContainer!.Name.Should().Be("test-container");
    }
}
```

#### E2E Tests
```csharp
public class ContainerManagementE2ETests : IClassFixture<ApplicationFixture>
{
    private readonly ApplicationFixture _fixture;
    
    public ContainerManagementE2ETests(ApplicationFixture fixture)
    {
        _fixture = fixture;
    }
    
    [Fact]
    public async Task CreateContainer_EndToEnd_ShouldCreateAndDisplayContainer()
    {
        // Arrange
        var app = _fixture.App;
        var mainWindow = app.GetMainWindow();
        
        // Act
        await mainWindow.ClickAsync("[data-testid=create-container-button]");
        await mainWindow.FillAsync("[data-testid=container-name-input]", "test-container");
        await mainWindow.FillAsync("[data-testid=container-image-input]", "nginx:latest");
        await mainWindow.ClickAsync("[data-testid=create-button]");
        
        // Assert
        await mainWindow.WaitForSelectorAsync("[data-testid=container-list]");
        var containerItems = await mainWindow.QuerySelectorAllAsync("[data-testid=container-item]");
        containerItems.Should().HaveCountGreaterThan(0);
        
        var containerName = await containerItems.First().TextContentAsync();
        containerName.Should().Contain("test-container");
    }
}
```

### Cobertura de Testes

#### Configuração
```xml
<!-- Directory.Build.props -->
<PropertyGroup Condition="'$(Configuration)' == 'Debug'">
  <CollectCoverage>true</CollectCoverage>
  <CoverletOutputFormat>opencover,lcov</CoverletOutputFormat>
  <CoverletOutput>./coverage/</CoverletOutput>
  <Exclude>[*.Tests]*,[*.TestHelpers]*</Exclude>
  <ExcludeByAttribute>Obsolete,GeneratedCodeAttribute,CompilerGeneratedAttribute</ExcludeByAttribute>
</PropertyGroup>
```

#### Metas de Cobertura
- **Mínimo**: 80% de cobertura geral
- **Domain**: 95% de cobertura
- **Application**: 90% de cobertura
- **Infrastructure**: 70% de cobertura
- **UI**: 60% de cobertura

---

## Documentação

### Documentação de Código

#### XML Documentation
```csharp
/// <summary>
/// Serviço responsável por gerenciar containers Docker e Podman
/// </summary>
/// <remarks>
/// Este serviço abstrai as diferenças entre Docker e Podman,
/// fornecendo uma interface unificada para operações de container.
/// </remarks>
public class ContainerService : IContainerService
{
    /// <summary>
    /// Cria um novo container baseado na configuração fornecida
    /// </summary>
    /// <param name="request">Configuração do container a ser criado</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da operação contendo o ID do container criado</returns>
    /// <exception cref="ArgumentNullException">Lançada quando <paramref name="request"/> é null</exception>
    /// <exception cref="InvalidOperationException">Lançada quando o engine de container não está disponível</exception>
    /// <example>
    /// <code>
    /// var request = new CreateContainerRequest
    /// {
    ///     Name = "my-container",
    ///     Image = "nginx:latest",
    ///     Ports = new[] { "80:80" }
    /// };
    /// 
    /// var result = await containerService.CreateAsync(request);
    /// if (result.IsSuccess)
    /// {
    ///     Console.WriteLine($"Container created: {result.Value}");
    /// }
    /// </code>
    /// </example>
    public async Task<Result<string>> CreateAsync(CreateContainerRequest request, CancellationToken cancellationToken = default)
    {
        // Implementação...
    }
}
```

### README Structure
```markdown
# Auto-Instalador Desktop

## Descrição
Breve descrição do projeto e seus objetivos.

## Funcionalidades
- Lista das principais funcionalidades

## Requisitos
- Requisitos de sistema
- Dependências

## Instalação
- Instruções de instalação
- Configuração inicial

## Uso
- Exemplos de uso
- Screenshots

## Desenvolvimento
- Como configurar ambiente de desenvolvimento
- Como contribuir

## Arquitetura
- Visão geral da arquitetura
- Diagramas

## API
- Documentação da API (se aplicável)

## Testes
- Como executar testes
- Cobertura de testes

## Deploy
- Instruções de deploy
- Configurações de produção

## Contribuição
- Guidelines para contribuição
- Code of conduct

## Licença
- Informações de licença
```

---

## Performance

### Diretrizes de Performance

#### 1. Async/Await
```csharp
// ✅ Correto
public async Task<IEnumerable<Container>> GetContainersAsync()
{
    return await _repository.GetAllAsync();
}

// ❌ Incorreto
public IEnumerable<Container> GetContainers()
{
    return _repository.GetAllAsync().Result; // Pode causar deadlock
}
```

#### 2. ConfigureAwait
```csharp
// ✅ Correto (em bibliotecas)
public async Task<Container> GetContainerAsync(Guid id)
{
    return await _repository.GetByIdAsync(id).ConfigureAwait(false);
}

// ✅ Correto (em UI)
public async Task LoadContainersAsync()
{
    var containers = await _service.GetContainersAsync(); // ConfigureAwait(true) é padrão
    Containers.Clear();
    Containers.AddRange(containers);
}
```

#### 3. Memory Management
```csharp
// ✅ Correto - Dispose de recursos
public class DockerService : IDockerService, IDisposable
{
    private readonly DockerClient _client;
    private bool _disposed;
    
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
    
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _client?.Dispose();
        }
        _disposed = true;
    }
}

// ✅ Correto - Using statements
public async Task<Stream> GetContainerLogsAsync(string containerId)
{
    using var response = await _client.Containers.GetContainerLogsAsync(containerId, new ContainerLogsParameters());
    return response;
}
```

#### 4. Caching
```csharp
public class ImageService : IImageService
{
    private readonly IMemoryCache _cache;
    private readonly IDockerService _dockerService;
    
    public async Task<IEnumerable<Image>> GetImagesAsync()
    {
        const string cacheKey = "docker_images";
        
        if (_cache.TryGetValue(cacheKey, out IEnumerable<Image> cachedImages))
        {
            return cachedImages;
        }
        
        var images = await _dockerService.GetImagesAsync();
        _cache.Set(cacheKey, images, TimeSpan.FromMinutes(5));
        
        return images;
    }
}
```

### Monitoramento de Performance

#### Métricas Importantes
- **Tempo de inicialização**: < 3 segundos
- **Tempo de resposta da UI**: < 100ms
- **Uso de memória**: < 200MB em idle
- **Tempo de carregamento de listas**: < 1 segundo

#### Profiling
```csharp
// Usar ILogger para métricas de performance
public class ContainerService : IContainerService
{
    private readonly ILogger<ContainerService> _logger;
    
    public async Task<Result<Container>> CreateAsync(CreateContainerRequest request)
    {
        using var activity = _logger.BeginScope("Creating container {ContainerName}", request.Name);
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var result = await _dockerService.CreateContainerAsync(request);
            
            _logger.LogInformation("Container {ContainerName} created in {ElapsedMs}ms", 
                request.Name, stopwatch.ElapsedMilliseconds);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create container {ContainerName} after {ElapsedMs}ms", 
                request.Name, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }
}
```

---

## Segurança

### Diretrizes de Segurança

#### 1. Validação de Entrada
```csharp
public class CreateContainerCommandValidator : AbstractValidator<CreateContainerCommand>
{
    public CreateContainerCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Container name is required")
            .Matches(@"^[a-zA-Z0-9][a-zA-Z0-9_.-]*$").WithMessage("Invalid container name format")
            .MaximumLength(255).WithMessage("Container name too long");
            
        RuleFor(x => x.Image)
            .NotEmpty().WithMessage("Image is required")
            .Must(BeValidImageName).WithMessage("Invalid image name");
            
        RuleFor(x => x.Environment)
            .Must(HaveValidEnvironmentVariables).WithMessage("Invalid environment variables");
    }
    
    private bool BeValidImageName(string imageName)
    {
        // Validar formato de imagem Docker/Podman
        var regex = new Regex(@"^[a-z0-9]+(?:[._-][a-z0-9]+)*(?:/[a-z0-9]+(?:[._-][a-z0-9]+)*)*(?::[a-zA-Z0-9_][a-zA-Z0-9._-]{0,127})?$");
        return regex.IsMatch(imageName);
    }
    
    private bool HaveValidEnvironmentVariables(Dictionary<string, string> environment)
    {
        if (environment == null) return true;
        
        foreach (var kvp in environment)
        {
            // Validar nomes de variáveis de ambiente
            if (!Regex.IsMatch(kvp.Key, @"^[a-zA-Z_][a-zA-Z0-9_]*$"))
                return false;
                
            // Verificar se não contém informações sensíveis
            if (ContainsSensitiveData(kvp.Key, kvp.Value))
                return false;
        }
        
        return true;
    }
}
```

#### 2. Sanitização de Logs
```csharp
public static class LoggerExtensions
{
    public static void LogContainerOperation(this ILogger logger, string operation, string containerName, object? additionalData = null)
    {
        // Sanitizar dados sensíveis antes de logar
        var sanitizedData = SanitizeLogData(additionalData);
        var sanitizedContainerName = SanitizeContainerName(containerName);
        
        logger.LogInformation("Container operation: {Operation} on {ContainerName}. Data: {@Data}", 
            operation, sanitizedContainerName, sanitizedData);
    }
    
    private static string SanitizeContainerName(string containerName)
    {
        // Remover caracteres potencialmente perigosos
        return Regex.Replace(containerName, @"[^a-zA-Z0-9_.-]", "_");
    }
    
    private static object? SanitizeLogData(object? data)
    {
        if (data == null) return null;
        
        // Implementar sanitização baseada no tipo de dados
        // Remover senhas, tokens, etc.
        return data;
    }
}
```

#### 3. Configuração Segura
```csharp
public class SecureConfigurationService
{
    private readonly IConfiguration _configuration;
    
    public string GetDockerHost()
    {
        var host = _configuration["Docker:Host"];
        
        // Validar se o host é seguro
        if (Uri.TryCreate(host, UriKind.Absolute, out var uri))
        {
            // Permitir apenas conexões locais ou HTTPS
            if (uri.IsLoopback || uri.Scheme == "https" || uri.Scheme == "npipe")
            {
                return host;
            }
        }
        
        throw new SecurityException("Insecure Docker host configuration");
    }
}
```

---

## Processo de Desenvolvimento

### Definition of Done

Uma funcionalidade é considerada "Done" quando:

1. **Código**:
   - [ ] Implementação completa
   - [ ] Code review aprovado
   - [ ] Padrões de código seguidos
   - [ ] Sem warnings de compilação

2. **Testes**:
   - [ ] Unit tests escritos e passando
   - [ ] Integration tests (se aplicável)
   - [ ] Cobertura de testes adequada
   - [ ] Testes de regressão passando

3. **Documentação**:
   - [ ] XML documentation atualizada
   - [ ] README atualizado (se necessário)
   - [ ] Changelog atualizado

4. **Qualidade**:
   - [ ] Performance adequada
   - [ ] Sem vazamentos de memória
   - [ ] Tratamento de erros implementado
   - [ ] Logs adequados

5. **UI/UX** (se aplicável):
   - [ ] Design aprovado
   - [ ] Responsivo
   - [ ] Acessível
   - [ ] Testado em diferentes resoluções

### Workflow de Desenvolvimento

1. **Planning**
   - Análise de requisitos
   - Design da solução
   - Estimativa de esforço

2. **Development**
   - Criar branch feature
   - Implementar funcionalidade
   - Escrever testes
   - Documentar código

3. **Testing**
   - Executar testes localmente
   - Testar manualmente
   - Verificar performance

4. **Review**
   - Criar Pull Request
   - Code review
   - Ajustes baseados no feedback

5. **Integration**
   - Merge para develop
   - CI/CD pipeline
   - Deploy para ambiente de teste

6. **Release**
   - Merge para main
   - Tag de versão
   - Deploy para produção

---

## Code Review

### Checklist de Code Review

#### Funcionalidade
- [ ] O código faz o que deveria fazer?
- [ ] A lógica está correta?
- [ ] Casos extremos são tratados?
- [ ] Tratamento de erros adequado?

#### Design
- [ ] Código bem estruturado?
- [ ] Princípios SOLID seguidos?
- [ ] Padrões de design apropriados?
- [ ] Responsabilidades bem definidas?

#### Complexidade
- [ ] Código fácil de entender?
- [ ] Funções/métodos não muito longos?
- [ ] Lógica não muito complexa?
- [ ] Pode ser simplificado?

#### Testes
- [ ] Testes adequados?
- [ ] Casos de teste cobrem cenários importantes?
- [ ] Testes são confiáveis?
- [ ] Mocks apropriados?

#### Nomenclatura
- [ ] Nomes descritivos?
- [ ] Convenções seguidas?
- [ ] Sem abreviações desnecessárias?
- [ ] Consistente com o resto do código?

#### Comentários
- [ ] Comentários úteis?
- [ ] Código autoexplicativo?
- [ ] XML documentation adequada?
- [ ] TODOs apropriados?

#### Performance
- [ ] Algoritmos eficientes?
- [ ] Sem vazamentos de memória?
- [ ] Async/await usado corretamente?
- [ ] Caching apropriado?

#### Segurança
- [ ] Validação de entrada?
- [ ] Dados sensíveis protegidos?
- [ ] Autorização adequada?
- [ ] Logs seguros?

### Processo de Review

1. **Preparação**
   - Revisar descrição do PR
   - Entender contexto e objetivos
   - Verificar se CI passou

2. **Review**
   - Revisar código linha por linha
   - Testar localmente se necessário
   - Verificar testes
   - Validar documentação

3. **Feedback**
   - Comentários construtivos
   - Sugestões de melhoria
   - Aprovação ou solicitação de mudanças

4. **Follow-up**
   - Verificar correções
   - Re-review se necessário
   - Aprovação final

Este documento serve como guia fundamental para manter a qualidade, consistência e escalabilidade do projeto Auto-Instalador Desktop, garantindo que todos os desenvolvedores sigam as mesmas práticas e padrões.