# Controles de Qualidade Automáticos
**Projeto**: Auto-Instalador Desktop Multiplataforma  
**Versão**: 1.0  
**Data**: Janeiro 2025

## 📋 Objetivo
Implementar controles automáticos de qualidade para garantir "desenvolvimento correto de primeira" e eliminar erros recorrentes sistemáticos.

---

## 🔧 Script de Validação de Dependências

### PowerShell Script (Windows)
```powershell
# ValidarDependencias.ps1
param(
    [string]$ProjectPath = ".",
    [switch]$Fix = $false
)

Write-Host "🔍 Validando dependências do projeto Auto-Instalador..." -ForegroundColor Green

# Versões esperadas
$ExpectedVersions = @{
    "Microsoft.NET.Sdk" = "net9.0"
    "Avalonia" = "11.3.4"
    "Docker.DotNet" = "3.125.15"
    "MediatR" = "12.4.1"  # Versão estável recomendada
    "FluentValidation" = "11.10.0"
    "Serilog" = "4.1.0"
    "ReactiveUI" = "20.1.63"
}

# Função para verificar arquivo .csproj
function Test-ProjectFile {
    param([string]$FilePath)
    
    if (-not (Test-Path $FilePath)) {
        Write-Warning "Arquivo não encontrado: $FilePath"
        return $false
    }
    
    $content = Get-Content $FilePath -Raw
    $xml = [xml]$content
    
    $issues = @()
    
    # Verificar TargetFramework
    $targetFramework = $xml.Project.PropertyGroup.TargetFramework
    if ($targetFramework -ne "net9.0") {
        $issues += "❌ TargetFramework incorreto: $targetFramework (esperado: net9.0)"
    }
    
    # Verificar PackageReferences
    $packageRefs = $xml.Project.ItemGroup.PackageReference
    foreach ($package in $packageRefs) {
        $name = $package.Include
        $version = $package.Version
        
        if ($ExpectedVersions.ContainsKey($name)) {
            if ($version -ne $ExpectedVersions[$name]) {
                $issues += "❌ $name versão incorreta: $version (esperado: $($ExpectedVersions[$name]))"
            } else {
                Write-Host "✅ $name versão correta: $version" -ForegroundColor Green
            }
        }
    }
    
    if ($issues.Count -gt 0) {
        Write-Host "Problemas encontrados em $FilePath:" -ForegroundColor Red
        $issues | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
        return $false
    }
    
    return $true
}

# Verificar todos os arquivos .csproj
$projectFiles = Get-ChildItem -Path $ProjectPath -Recurse -Filter "*.csproj"
$allValid = $true

foreach ($file in $projectFiles) {
    Write-Host "`n📁 Verificando: $($file.Name)" -ForegroundColor Yellow
    $isValid = Test-ProjectFile -FilePath $file.FullName
    $allValid = $allValid -and $isValid
}

# Verificar Directory.Build.props
$buildPropsPath = Join-Path $ProjectPath "Directory.Build.props"
if (Test-Path $buildPropsPath) {
    Write-Host "`n📁 Verificando: Directory.Build.props" -ForegroundColor Yellow
    Test-ProjectFile -FilePath $buildPropsPath
}

if ($allValid) {
    Write-Host "`n✅ Todas as dependências estão corretas!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n❌ Inconsistências encontradas nas dependências!" -ForegroundColor Red
    exit 1
}
```

### Bash Script (Linux/macOS)
```bash
#!/bin/bash
# validar_dependencias.sh

echo "🔍 Validando dependências do projeto Auto-Instalador..."

# Cores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Função para verificar versões
check_version() {
    local package=$1
    local expected=$2
    local found=$3
    
    if [ "$found" = "$expected" ]; then
        echo -e "${GREEN}✅ $package versão correta: $found${NC}"
        return 0
    else
        echo -e "${RED}❌ $package versão incorreta: $found (esperado: $expected)${NC}"
        return 1
    fi
}

# Verificar se dotnet está instalado
if ! command -v dotnet &> /dev/null; then
    echo -e "${RED}❌ .NET SDK não encontrado!${NC}"
    exit 1
fi

# Verificar versão do .NET
dotnet_version=$(dotnet --version)
if [[ $dotnet_version == 9.* ]]; then
    echo -e "${GREEN}✅ .NET SDK versão correta: $dotnet_version${NC}"
else
    echo -e "${RED}❌ .NET SDK versão incorreta: $dotnet_version (esperado: 9.x)${NC}"
    exit 1
fi

# Verificar se o projeto compila
echo -e "${YELLOW}🔨 Verificando compilação...${NC}"
if dotnet build --no-restore --verbosity quiet; then
    echo -e "${GREEN}✅ Projeto compila sem erros${NC}"
else
    echo -e "${RED}❌ Projeto não compila!${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Validação concluída com sucesso!${NC}"
```

---

## 🧪 Script de Validação de Código

### Validação de Padrões MediatR
```powershell
# ValidarPadroesMediatR.ps1
function Test-MediatRPatterns {
    param([string]$SourcePath)
    
    $issues = @()
    $csFiles = Get-ChildItem -Path $SourcePath -Recurse -Filter "*.cs"
    
    foreach ($file in $csFiles) {
        $content = Get-Content $file.FullName -Raw
        
        # Verificar handlers void que retornam Task<Unit> (padrão obsoleto)
        if ($content -match "Task<Unit>.*Handle.*IRequest[^<]") {
            $issues += "❌ $($file.Name): Handler void deve retornar Task, não Task<Unit>"
        }
        
        # Verificar uso de Unit.Value (padrão obsoleto)
        if ($content -match "Unit\.Value") {
            $issues += "❌ $($file.Name): Use 'return;' em vez de 'Unit.Value'"
        }
        
        # Verificar registro correto do MediatR
        if ($content -match "AddMediatR\(" -and -not ($content -match "cfg\.RegisterServicesFromAssembly")) {
            $issues += "❌ $($file.Name): Use cfg.RegisterServicesFromAssembly() para registro do MediatR"
        }
    }
    
    return $issues
}

$mediatRIssues = Test-MediatRPatterns -SourcePath "src"
if ($mediatRIssues.Count -gt 0) {
    Write-Host "Problemas encontrados nos padrões MediatR:" -ForegroundColor Red
    $mediatRIssues | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
} else {
    Write-Host "✅ Padrões MediatR estão corretos!" -ForegroundColor Green
}
```

### Validação de Padrões Avalonia UI
```powershell
# ValidarPadroesAvalonia.ps1
function Test-AvaloniaPatterns {
    param([string]$SourcePath)
    
    $issues = @()
    $csFiles = Get-ChildItem -Path $SourcePath -Recurse -Filter "*.cs" | Where-Object { $_.Directory.Name -eq "ViewModels" }
    
    foreach ($file in $csFiles) {
        $content = Get-Content $file.FullName -Raw
        
        # Verificar se ViewModels herdam de ReactiveObject
        if ($content -match "class.*ViewModel" -and -not ($content -match ": ReactiveObject")) {
            $issues += "❌ $($file.Name): ViewModel deve herdar de ReactiveObject"
        }
        
        # Verificar uso de RaiseAndSetIfChanged
        if ($content -match "set\s*{.*=.*}" -and -not ($content -match "RaiseAndSetIfChanged")) {
            $issues += "⚠️ $($file.Name): Considere usar RaiseAndSetIfChanged para propriedades"
        }
        
        # Verificar uso de ReactiveCommand
        if ($content -match "ICommand" -and -not ($content -match "ReactiveCommand")) {
            $issues += "⚠️ $($file.Name): Considere usar ReactiveCommand em vez de ICommand"
        }
    }
    
    return $issues
}

$avaloniaIssues = Test-AvaloniaPatterns -SourcePath "src"
if ($avaloniaIssues.Count -gt 0) {
    Write-Host "Problemas encontrados nos padrões Avalonia:" -ForegroundColor Red
    $avaloniaIssues | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
} else {
    Write-Host "✅ Padrões Avalonia estão corretos!" -ForegroundColor Green
}
```

---

## 🔄 GitHub Actions Workflow

### Validação Automática CI/CD
```yaml
# .github/workflows/quality-control.yml
name: Quality Control

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  dependency-validation:
    runs-on: ubuntu-latest
    name: Validate Dependencies
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'
    
    - name: Validate Dependencies
      run: |
        chmod +x ./scripts/validar_dependencias.sh
        ./scripts/validar_dependencias.sh
    
    - name: Check for obsolete APIs
      run: |
        # Verificar uso de APIs obsoletas
        if grep -r "Task<Unit>" src/ --include="*.cs"; then
          echo "❌ Encontrado uso de Task<Unit> (obsoleto)"
          exit 1
        fi
        
        if grep -r "Unit.Value" src/ --include="*.cs"; then
          echo "❌ Encontrado uso de Unit.Value (obsoleto)"
          exit 1
        fi
        
        echo "✅ Nenhuma API obsoleta encontrada"

  code-quality:
    runs-on: ubuntu-latest
    name: Code Quality Check
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build
      run: dotnet build --no-restore --configuration Release
    
    - name: Run tests
      run: dotnet test --no-build --configuration Release --verbosity normal
    
    - name: Code analysis
      run: |
        dotnet build --no-restore --configuration Release \
          --verbosity normal \
          -p:TreatWarningsAsErrors=true \
          -p:RunAnalyzersDuringBuild=true

  architecture-validation:
    runs-on: ubuntu-latest
    name: Architecture Validation
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Validate Clean Architecture
      run: |
        # Verificar se Core não referencia outras camadas
        if grep -r "AutoInstaller.Infrastructure\|AutoInstaller.UI" src/AutoInstaller.Core/ --include="*.cs"; then
          echo "❌ Core layer não deve referenciar Infrastructure ou UI"
          exit 1
        fi
        
        # Verificar se Application não referencia UI
        if grep -r "AutoInstaller.UI" src/AutoInstaller.Application/ --include="*.cs"; then
          echo "❌ Application layer não deve referenciar UI"
          exit 1
        fi
        
        echo "✅ Arquitetura Clean está correta"
```

---

## 📊 Métricas de Qualidade

### Dashboard de Qualidade
```json
{
  "quality_metrics": {
    "dependency_consistency": "100%",
    "obsolete_api_usage": "0%",
    "test_coverage": ">80%",
    "architecture_compliance": "100%",
    "code_analysis_warnings": "0",
    "build_success_rate": "100%"
  },
  "validation_rules": {
    "mediatr_patterns": "enforced",
    "avalonia_patterns": "enforced",
    "docker_dotnet_only": "enforced",
    "clean_architecture": "enforced",
    "documentation_consultation": "required"
  }
}
```

### Relatório Automático
```powershell
# GerarRelatorioQualidade.ps1
$report = @{
    "timestamp" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "project" = "Auto-Instalador Desktop"
    "validations" = @()
}

# Executar todas as validações
$validations = @(
    @{ name = "Dependencies"; script = "ValidarDependencias.ps1" },
    @{ name = "MediatR Patterns"; script = "ValidarPadroesMediatR.ps1" },
    @{ name = "Avalonia Patterns"; script = "ValidarPadroesAvalonia.ps1" }
)

foreach ($validation in $validations) {
    try {
        & $validation.script
        $report.validations += @{
            name = $validation.name
            status = "PASS"
            timestamp = Get-Date -Format "HH:mm:ss"
        }
    }
    catch {
        $report.validations += @{
            name = $validation.name
            status = "FAIL"
            error = $_.Exception.Message
            timestamp = Get-Date -Format "HH:mm:ss"
        }
    }
}

# Salvar relatório
$report | ConvertTo-Json -Depth 3 | Out-File "quality-report.json"
Write-Host "📊 Relatório de qualidade gerado: quality-report.json"
```

---

## 🎯 Integração com IDE

### VS Code Settings
```json
{
  "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true,
  "omnisharp.enableRoslynAnalyzers": true,
  "omnisharp.enableEditorConfigSupport": true,
  "files.associations": {
    "*.axaml": "xml"
  },
  "emmet.includeLanguages": {
    "axaml": "xml"
  }
}
```

### EditorConfig
```ini
# .editorconfig
root = true

[*.cs]
# Enforce code style
dotnet_analyzer_diagnostic.category-style.severity = warning
dotnet_analyzer_diagnostic.IDE0055.severity = warning

# Specific rules for Auto-Instalador
dotnet_diagnostic.CA1031.severity = none  # Do not catch general exception types
dotnet_diagnostic.CS1591.severity = none  # Missing XML comment
```

**OBJETIVO FINAL**: Eliminar 100% dos erros recorrentes e garantir desenvolvimento assertivo na primeira tentativa.
