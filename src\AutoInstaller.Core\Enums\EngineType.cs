namespace AutoInstaller.Core.Enums;

/// <summary>
/// Tipos de engines de container suportadas pelo sistema autônomo
/// </summary>
public enum EngineType
{
    /// <summary>
    /// Docker Engine embarcado
    /// </summary>
    Docker = 1,

    /// <summary>
    /// Podman Engine embarcado (rootless)
    /// </summary>
    Podman = 2
}

/// <summary>
/// Status das engines embarcadas
/// </summary>
public enum EngineStatus
{
    /// <summary>
    /// Engine não foi extraída ainda
    /// </summary>
    NotExtracted = 0,

    /// <summary>
    /// Engine foi extraída mas não configurada
    /// </summary>
    Extracted = 1,

    /// <summary>
    /// Engine foi configurada mas não está rodando
    /// </summary>
    Configured = 2,

    /// <summary>
    /// Engine está iniciando
    /// </summary>
    Starting = 3,

    /// <summary>
    /// Engine está em execução
    /// </summary>
    Running = 4,

    /// <summary>
    /// Engine está parando
    /// </summary>
    Stopping = 5,

    /// <summary>
    /// Engine foi parada
    /// </summary>
    Stopped = 6,

    /// <summary>
    /// Engine está com erro
    /// </summary>
    Error = 7
}

/// <summary>
/// Status dos containers
/// </summary>
public enum ContainerStatus
{
    /// <summary>
    /// Container foi criado mas não iniciado
    /// </summary>
    Created = 0,

    /// <summary>
    /// Container está iniciando
    /// </summary>
    Starting = 1,

    /// <summary>
    /// Container está em execução
    /// </summary>
    Running = 2,

    /// <summary>
    /// Container está parando
    /// </summary>
    Stopping = 3,

    /// <summary>
    /// Container foi parado
    /// </summary>
    Stopped = 4,

    /// <summary>
    /// Container está pausado
    /// </summary>
    Paused = 5,

    /// <summary>
    /// Container está com erro
    /// </summary>
    Error = 6,

    /// <summary>
    /// Container foi removido
    /// </summary>
    Removed = 7
}

/// <summary>
/// Políticas de restart para containers
/// </summary>
public enum RestartPolicy
{
    /// <summary>
    /// Nunca reiniciar automaticamente
    /// </summary>
    No = 0,

    /// <summary>
    /// Sempre reiniciar
    /// </summary>
    Always = 1,

    /// <summary>
    /// Reiniciar apenas em caso de falha
    /// </summary>
    OnFailure = 2,

    /// <summary>
    /// Reiniciar a menos que seja parado manualmente
    /// </summary>
    UnlessStopped = 3
}

/// <summary>
/// Tipos de sistema operacional suportados
/// </summary>
public enum OperatingSystem
{
    /// <summary>
    /// Windows 10/11
    /// </summary>
    Windows = 1,

    /// <summary>
    /// Linux (Ubuntu/Debian)
    /// </summary>
    Linux = 2,

    /// <summary>
    /// macOS (futuro suporte)
    /// </summary>
    MacOS = 3
}

/// <summary>
/// Status de saúde das engines
/// </summary>
public enum HealthStatus
{
    /// <summary>
    /// Status desconhecido
    /// </summary>
    Unknown = 0,

    /// <summary>
    /// Engine saudável
    /// </summary>
    Healthy = 1,

    /// <summary>
    /// Engine com problemas
    /// </summary>
    Unhealthy = 2,

    /// <summary>
    /// Engine iniciando (ainda verificando saúde)
    /// </summary>
    Starting = 3
}

/// <summary>
/// Tipos de estratégia de fallback
/// </summary>
public enum FallbackStrategy
{
    /// <summary>
    /// Usar engine embarcada sempre
    /// </summary>
    AlwaysEmbedded = 0,

    /// <summary>
    /// Usar engine existente se disponível, senão embarcada
    /// </summary>
    PreferExisting = 1,

    /// <summary>
    /// Usar engine embarcada se existente não funcionar
    /// </summary>
    PreferEmbedded = 2,

    /// <summary>
    /// Perguntar ao usuário qual usar
    /// </summary>
    AskUser = 3
}
