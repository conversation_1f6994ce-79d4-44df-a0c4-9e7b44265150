# Deployment e Distribuição - Auto-Instalador Desktop Multiplataforma Autônomo

## Índice
1. [Estratégia de Deployment Autônomo](#estratégia-de-deployment-autônomo)
2. [Empacotamento com Engines Embarcadas](#empacotamento-com-engines-embarcadas)
3. [Build Multiplataforma com Binários](#build-multiplataforma-com-binários)
4. [Distribuição Self-Contained](#distribuição-self-contained)
5. [Auto-Update com Engines](#auto-update-com-engines)
6. [CI/CD Pipeline para Sistema Autônomo](#cicd-pipeline-para-sistema-autônomo)
7. [Monitoramento de Engines](#monitoramento-de-engines)
8. [Rollback e Versionamento](#rollback-e-versionamento)
9. [Configurações de Ambiente](#configurações-de-ambiente)
10. [Troubleshooting Engines Embarcadas](#troubleshooting-engines-embarcadas)

---

## Estratégia de Deployment Autônomo

### Ambientes de Deployment com Engines Embarcadas

```
Development → Staging → Production
     ↓           ↓          ↓
   Local      Preview    Release
   Build      Build      Build
     +          +          +
  Engines    Engines    Engines
 Embedded   Embedded   Embedded
```

### Plataformas Suportadas com Engines Embarcadas

| Plataforma | Arquitetura | Runtime | Engines Incluídas | Formato de Distribuição |
|------------|-------------|---------|-------------------|------------------------|
| Windows 10/11 | x64 | .NET 9 Self-Contained | Docker + Podman | MSI, MSIX, Portable |
| Ubuntu 20.04+ | x64 | .NET 9 Self-Contained | Docker + Podman | DEB, AppImage, Tar.gz |
| Debian 11+ | x64 | .NET 9 Self-Contained | Docker + Podman | DEB, AppImage, Tar.gz |

### Características do Deployment Autônomo
- **Self-Contained**: Inclui .NET 9 Runtime
- **Engines Embarcadas**: Docker Engine e Podman Engine inclusos
- **Zero Dependencies**: Funciona sem instalações prévias
- **Detecção Automática**: Identifica SO e arquitetura
- **Sistema de Fallback**: Detecta engines existentes
- **Tamanho do Pacote**: ~500MB (aplicação + engines + runtime)

### Estratégia de Versionamento com Engines

**Semantic Versioning (SemVer)**:
- **MAJOR.MINOR.PATCH** (ex: 1.2.3)
- **MAJOR**: Mudanças incompatíveis ou novas versões de engines
- **MINOR**: Novas funcionalidades compatíveis
- **PATCH**: Correções de bugs

**Build Numbers com Engine Versions**:
- **1.2.3.BUILD-docker.VERSION-podman.VERSION**
- Exemplo: 1.2.3.20240115-docker.24.0.7-podman.4.8.2

### Estrutura do Pacote Autônomo
```
auto-instalador-max-1.2.3-windows-x64/
├── AutoInstaller.exe                    # Aplicação principal
├── AutoInstaller.dll                    # Bibliotecas .NET
├── runtimes/                           # .NET 9 Runtime embarcado
├── engines/
│   ├── docker/
│   │   └── windows/
│   │       ├── dockerd.exe             # Docker Engine
│   │       ├── docker.exe              # Docker CLI
│   │       └── docker-init.exe         # Init Process
│   └── podman/
│       └── windows/
│           ├── podman.exe              # Podman Engine
│           └── conmon.exe              # Container Monitor
├── config/                             # Configurações padrão
├── data/                               # Diretório de dados
└── logs/                               # Diretório de logs
```

---

## Configuração Multiplataforma

### Arquivo de Projeto Principal

```xml
<!-- AutoInstaller.UI.csproj -->
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
    
    <!-- Informações da Aplicação -->
    <AssemblyTitle>Auto-Instalador Desktop</AssemblyTitle>
    <AssemblyDescription>Gerenciador de containers Docker e Podman</AssemblyDescription>
    <AssemblyCompany>Auto-Instalador Team</AssemblyCompany>
    <AssemblyProduct>Auto-Instalador Desktop</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <InformationalVersion>1.0.0</InformationalVersion>
    
    <!-- Configurações de Build -->
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <PublishReadyToRun>true</PublishReadyToRun>
    <PublishTrimmed>true</PublishTrimmed>
    <TrimMode>link</TrimMode>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
  </PropertyGroup>

  <!-- Configurações Específicas por Plataforma -->
  <PropertyGroup Condition="'$(RuntimeIdentifier)' == 'win-x64'">
    <ApplicationIcon>Assets\icon.ico</ApplicationIcon>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(RuntimeIdentifier)' == 'osx-x64' OR '$(RuntimeIdentifier)' == 'osx-arm64'">
    <ApplicationIcon>Assets\icon.icns</ApplicationIcon>
    <CFBundleName>Auto-Instalador Desktop</CFBundleName>
    <CFBundleDisplayName>Auto-Instalador Desktop</CFBundleDisplayName>
    <CFBundleIdentifier>com.autoinstalador.desktop</CFBundleIdentifier>
    <CFBundleVersion>1.0.0</CFBundleVersion>
    <CFBundleShortVersionString>1.0.0</CFBundleShortVersionString>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(RuntimeIdentifier)' == 'linux-x64' OR '$(RuntimeIdentifier)' == 'linux-arm64'">
    <ApplicationIcon>Assets\icon.png</ApplicationIcon>
  </PropertyGroup>

  <!-- Dependências Avalonia -->
  <PackageReference Include="Avalonia" Version="11.3.4" />
  <PackageReference Include="Avalonia.Desktop" Version="11.3.4" />
  <PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.4" />
  <PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.4" />
  <PackageReference Include="Avalonia.ReactiveUI" Version="11.3.4" />
  
  <!-- Dependências de Container -->
  <PackageReference Include="Docker.DotNet" Version="3.125.15" />
  <PackageReference Include="PodMan.DotNet" Version="1.0.4" />
  
  <!-- Outras Dependências -->
  <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
  <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
  <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
  <PackageReference Include="MediatR" Version="13.0.0" />
</Project>
```

### Directory.Build.props

```xml
<Project>
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    
    <!-- Configurações de Build -->
    <Deterministic>true</Deterministic>
    <ContinuousIntegrationBuild Condition="'$(CI)' == 'true'">true</ContinuousIntegrationBuild>
    
    <!-- Configurações de Análise -->
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <CodeAnalysisRuleSet>$(MSBuildThisFileDirectory)CodeAnalysis.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  
  <!-- Informações de Versionamento -->
  <PropertyGroup>
    <VersionPrefix>1.0.0</VersionPrefix>
    <VersionSuffix Condition="'$(Configuration)' == 'Debug'">dev</VersionSuffix>
    <BuildNumber Condition="'$(BuildNumber)' == ''">$([System.DateTime]::Now.ToString('yyyyMMdd'))</BuildNumber>
    <FileVersion>$(VersionPrefix).$(BuildNumber)</FileVersion>
  </PropertyGroup>
</Project>
```

---

## Build e Empacotamento

### Scripts de Build

#### build.ps1 (Windows)

```powershell
#!/usr/bin/env pwsh

param(
    [string]$Configuration = "Release",
    [string]$Platform = "Any CPU",
    [string]$OutputPath = "./artifacts",
    [switch]$Clean,
    [switch]$Pack,
    [switch]$Publish
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 Auto-Instalador Desktop Build Script" -ForegroundColor Green
Write-Host "Configuration: $Configuration" -ForegroundColor Yellow
Write-Host "Platform: $Platform" -ForegroundColor Yellow
Write-Host "Output Path: $OutputPath" -ForegroundColor Yellow

# Limpar se solicitado
if ($Clean) {
    Write-Host "🧹 Limpando artifacts anteriores..." -ForegroundColor Blue
    Remove-Item -Path $OutputPath -Recurse -Force -ErrorAction SilentlyContinue
    dotnet clean --configuration $Configuration
}

# Criar diretório de saída
New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null

# Restaurar dependências
Write-Host "📦 Restaurando dependências..." -ForegroundColor Blue
dotnet restore

# Build da solução
Write-Host "🔨 Compilando solução..." -ForegroundColor Blue
dotnet build --configuration $Configuration --no-restore

# Executar testes
Write-Host "🧪 Executando testes..." -ForegroundColor Blue
dotnet test --configuration $Configuration --no-build --logger trx --results-directory "$OutputPath/TestResults"

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Testes falharam!"
    exit $LASTEXITCODE
}

# Publicar se solicitado
if ($Publish) {
    Write-Host "📦 Publicando aplicação..." -ForegroundColor Blue
    
    $runtimes = @("win-x64", "win-arm64", "osx-x64", "osx-arm64", "linux-x64", "linux-arm64")
    
    foreach ($runtime in $runtimes) {
        Write-Host "  📱 Publicando para $runtime..." -ForegroundColor Cyan
        
        $publishPath = "$OutputPath/publish/$runtime"
        
        dotnet publish src/AutoInstaller.UI/AutoInstaller.UI.csproj `
            --configuration $Configuration `
            --runtime $runtime `
            --self-contained true `
            --output $publishPath `
            /p:PublishSingleFile=true `
            /p:PublishReadyToRun=true `
            /p:PublishTrimmed=true
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "❌ Falha na publicação para $runtime!"
            exit $LASTEXITCODE
        }
    }
}

# Empacotar se solicitado
if ($Pack) {
    Write-Host "📦 Criando pacotes de distribuição..." -ForegroundColor Blue
    
    # Windows MSI
    if (Get-Command "candle.exe" -ErrorAction SilentlyContinue) {
        Write-Host "  🪟 Criando MSI para Windows..." -ForegroundColor Cyan
        & "$PSScriptRoot/packaging/windows/build-msi.ps1" -OutputPath $OutputPath
    }
    
    # macOS DMG
    if ($IsMacOS) {
        Write-Host "  🍎 Criando DMG para macOS..." -ForegroundColor Cyan
        & "$PSScriptRoot/packaging/macos/build-dmg.sh" "$OutputPath"
    }
    
    # Linux packages
    if ($IsLinux) {
        Write-Host "  🐧 Criando pacotes para Linux..." -ForegroundColor Cyan
        & "$PSScriptRoot/packaging/linux/build-packages.sh" "$OutputPath"
    }
}

Write-Host "✅ Build concluído com sucesso!" -ForegroundColor Green
Write-Host "📁 Artifacts disponíveis em: $OutputPath" -ForegroundColor Yellow
```

#### build.sh (Linux/macOS)

```bash
#!/bin/bash

set -e

CONFIGURATION="Release"
OUTPUT_PATH="./artifacts"
CLEAN=false
PACK=false
PUBLISH=false

# Parse argumentos
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--configuration)
            CONFIGURATION="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_PATH="$2"
            shift 2
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        --pack)
            PACK=true
            shift
            ;;
        --publish)
            PUBLISH=true
            shift
            ;;
        *)
            echo "Argumento desconhecido: $1"
            exit 1
            ;;
    esac
done

echo "🚀 Auto-Instalador Desktop Build Script"
echo "Configuration: $CONFIGURATION"
echo "Output Path: $OUTPUT_PATH"

# Limpar se solicitado
if [ "$CLEAN" = true ]; then
    echo "🧹 Limpando artifacts anteriores..."
    rm -rf "$OUTPUT_PATH"
    dotnet clean --configuration "$CONFIGURATION"
fi

# Criar diretório de saída
mkdir -p "$OUTPUT_PATH"

# Restaurar dependências
echo "📦 Restaurando dependências..."
dotnet restore

# Build da solução
echo "🔨 Compilando solução..."
dotnet build --configuration "$CONFIGURATION" --no-restore

# Executar testes
echo "🧪 Executando testes..."
dotnet test --configuration "$CONFIGURATION" --no-build --logger trx --results-directory "$OUTPUT_PATH/TestResults"

# Publicar se solicitado
if [ "$PUBLISH" = true ]; then
    echo "📦 Publicando aplicação..."
    
    runtimes=("win-x64" "win-arm64" "osx-x64" "osx-arm64" "linux-x64" "linux-arm64")
    
    for runtime in "${runtimes[@]}"; do
        echo "  📱 Publicando para $runtime..."
        
        publish_path="$OUTPUT_PATH/publish/$runtime"
        
        dotnet publish src/AutoInstaller.UI/AutoInstaller.UI.csproj \
            --configuration "$CONFIGURATION" \
            --runtime "$runtime" \
            --self-contained true \
            --output "$publish_path" \
            /p:PublishSingleFile=true \
            /p:PublishReadyToRun=true \
            /p:PublishTrimmed=true
    done
fi

# Empacotar se solicitado
if [ "$PACK" = true ]; then
    echo "📦 Criando pacotes de distribuição..."
    
    # macOS DMG
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "  🍎 Criando DMG para macOS..."
        "./packaging/macos/build-dmg.sh" "$OUTPUT_PATH"
    fi
    
    # Linux packages
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "  🐧 Criando pacotes para Linux..."
        "./packaging/linux/build-packages.sh" "$OUTPUT_PATH"
    fi
fi

echo "✅ Build concluído com sucesso!"
echo "📁 Artifacts disponíveis em: $OUTPUT_PATH"
```

### Configuração de Trimming

#### TrimmerRoots.xml

```xml
<linker>
  <!-- Preservar assemblies Avalonia -->
  <assembly fullname="Avalonia.Base" />
  <assembly fullname="Avalonia.Controls" />
  <assembly fullname="Avalonia.DesktopRuntime" />
  <assembly fullname="Avalonia.Markup.Xaml" />
  <assembly fullname="Avalonia.Themes.Fluent" />
  
  <!-- Preservar assemblies Docker/Podman -->
  <assembly fullname="Docker.DotNet" />
  <assembly fullname="PodMan.DotNet" />
  
  <!-- Preservar assemblies de reflexão -->
  <assembly fullname="System.Text.Json" />
  <assembly fullname="Microsoft.Extensions.DependencyInjection" />
  <assembly fullname="MediatR" />
  
  <!-- Preservar tipos específicos -->
  <type fullname="AutoInstaller.UI.ViewModels.*" />
  <type fullname="AutoInstaller.UI.Views.*" />
  <type fullname="AutoInstaller.Application.Commands.*" />
  <type fullname="AutoInstaller.Application.Queries.*" />
</linker>
```

---

## Distribuição

### Windows

#### MSI Installer (WiX)

```xml
<!-- packaging/windows/AutoInstaller.wxs -->
<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="*" 
           Name="Auto-Instalador Desktop" 
           Language="1046" 
           Version="!(bind.FileVersion.AutoInstallerExe)" 
           Manufacturer="Auto-Instalador Team" 
           UpgradeCode="{12345678-1234-1234-1234-123456789012}">
    
    <Package InstallerVersion="200" 
             Compressed="yes" 
             InstallScope="perMachine" 
             Description="Gerenciador de containers Docker e Podman" />
    
    <MajorUpgrade DowngradeErrorMessage="Uma versão mais recente já está instalada." />
    
    <MediaTemplate EmbedCab="yes" />
    
    <Feature Id="ProductFeature" Title="Auto-Instalador Desktop" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
      <ComponentRef Id="ApplicationShortcut" />
      <ComponentRef Id="DesktopShortcut" />
    </Feature>
    
    <!-- Diretórios -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFiles64Folder">
        <Directory Id="INSTALLFOLDER" Name="Auto-Instalador Desktop" />
      </Directory>
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="Auto-Instalador Desktop" />
      </Directory>
      <Directory Id="DesktopFolder" Name="Desktop" />
    </Directory>
    
    <!-- Componentes -->
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <Component Id="AutoInstallerExe" Guid="{*************-4321-4321-************}">
        <File Id="AutoInstallerExe" 
              Source="$(var.PublishDir)\AutoInstaller.UI.exe" 
              KeyPath="yes" 
              Checksum="yes" />
      </Component>
      
      <!-- Arquivos de configuração -->
      <Component Id="ConfigFiles" Guid="{11111111-**************-************}">
        <File Id="AppSettingsJson" 
              Source="$(var.PublishDir)\appsettings.json" />
      </Component>
    </ComponentGroup>
    
    <!-- Atalhos -->
    <Component Id="ApplicationShortcut" Directory="ApplicationProgramsFolder" Guid="{2222**************-5555-************}">
      <Shortcut Id="ApplicationStartMenuShortcut"
                Name="Auto-Instalador Desktop"
                Description="Gerenciador de containers Docker e Podman"
                Target="[#AutoInstallerExe]"
                WorkingDirectory="INSTALLFOLDER" />
      <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />
      <RegistryValue Root="HKCU" 
                     Key="Software\AutoInstaller\Desktop" 
                     Name="installed" 
                     Type="integer" 
                     Value="1" 
                     KeyPath="yes" />
    </Component>
    
    <Component Id="DesktopShortcut" Directory="DesktopFolder" Guid="{*************-5555-6666-************}">
      <Shortcut Id="ApplicationDesktopShortcut"
                Name="Auto-Instalador Desktop"
                Description="Gerenciador de containers Docker e Podman"
                Target="[#AutoInstallerExe]"
                WorkingDirectory="INSTALLFOLDER" />
      <RegistryValue Root="HKCU" 
                     Key="Software\AutoInstaller\Desktop" 
                     Name="desktop_shortcut" 
                     Type="integer" 
                     Value="1" 
                     KeyPath="yes" />
    </Component>
  </Product>
</Wix>
```

#### MSIX Package

```xml
<!-- packaging/windows/Package.appxmanifest -->
<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
         xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
         xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities">
  
  <Identity Name="AutoInstaller.Desktop"
            Publisher="CN=Auto-Instalador Team"
            Version="*******" />
  
  <Properties>
    <DisplayName>Auto-Instalador Desktop</DisplayName>
    <PublisherDisplayName>Auto-Instalador Team</PublisherDisplayName>
    <Logo>Assets\StoreLogo.png</Logo>
    <Description>Gerenciador de containers Docker e Podman</Description>
  </Properties>
  
  <Dependencies>
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.17763.0" MaxVersionTested="10.0.22000.0" />
  </Dependencies>
  
  <Capabilities>
    <rescap:Capability Name="runFullTrust" />
  </Capabilities>
  
  <Applications>
    <Application Id="AutoInstallerDesktop" Executable="AutoInstaller.UI.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements DisplayName="Auto-Instalador Desktop"
                          Description="Gerenciador de containers Docker e Podman"
                          BackgroundColor="#1e1e1e"
                          Square150x150Logo="Assets\Square150x150Logo.png"
                          Square44x44Logo="Assets\Square44x44Logo.png" />
    </Application>
  </Applications>
</Package>
```

### macOS

#### Info.plist

```xml
<!-- packaging/macos/Info.plist -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleName</key>
    <string>Auto-Instalador Desktop</string>
    
    <key>CFBundleDisplayName</key>
    <string>Auto-Instalador Desktop</string>
    
    <key>CFBundleIdentifier</key>
    <string>com.autoinstalador.desktop</string>
    
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    
    <key>CFBundleExecutable</key>
    <string>AutoInstaller.UI</string>
    
    <key>CFBundleIconFile</key>
    <string>icon.icns</string>
    
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    
    <key>NSHighResolutionCapable</key>
    <true/>
    
    <key>NSSupportsAutomaticGraphicsSwitching</key>
    <true/>
    
    <key>LSApplicationCategoryType</key>
    <string>public.app-category.developer-tools</string>
    
    <key>NSHumanReadableCopyright</key>
    <string>Copyright © 2024 Auto-Instalador Team</string>
</dict>
</plist>
```

#### DMG Creation Script

```bash
#!/bin/bash
# packaging/macos/build-dmg.sh

set -e

OUTPUT_PATH="$1"
APP_NAME="Auto-Instalador Desktop"
DMG_NAME="AutoInstaller-Desktop"
VERSION="1.0.0"

echo "🍎 Criando DMG para macOS..."

# Criar estrutura do app bundle
APP_BUNDLE="$OUTPUT_PATH/$APP_NAME.app"
mkdir -p "$APP_BUNDLE/Contents/MacOS"
mkdir -p "$APP_BUNDLE/Contents/Resources"

# Copiar executável
cp "$OUTPUT_PATH/publish/osx-x64/AutoInstaller.UI" "$APP_BUNDLE/Contents/MacOS/"
chmod +x "$APP_BUNDLE/Contents/MacOS/AutoInstaller.UI"

# Copiar Info.plist
cp "packaging/macos/Info.plist" "$APP_BUNDLE/Contents/"

# Copiar ícone
cp "Assets/icon.icns" "$APP_BUNDLE/Contents/Resources/"

# Criar DMG temporário
TEMP_DMG="$OUTPUT_PATH/temp.dmg"
FINAL_DMG="$OUTPUT_PATH/$DMG_NAME-$VERSION.dmg"

# Calcular tamanho necessário
SIZE=$(du -sm "$APP_BUNDLE" | cut -f1)
SIZE=$((SIZE + 50)) # Adicionar margem

# Criar imagem temporária
hdiutil create -srcfolder "$APP_BUNDLE" -volname "$APP_NAME" -fs HFS+ -fsargs "-c c=64,a=16,e=16" -format UDRW -size ${SIZE}m "$TEMP_DMG"

# Montar DMG temporário
DEVICE=$(hdiutil attach -readwrite -noverify "$TEMP_DMG" | egrep '^/dev/' | sed 1q | awk '{print $1}')
VOLUME_PATH="/Volumes/$APP_NAME"

# Configurar aparência do DMG
echo 'tell application "Finder"
    tell disk "'$APP_NAME'"
        open
        set current view of container window to icon view
        set toolbar visible of container window to false
        set statusbar visible of container window to false
        set the bounds of container window to {400, 100, 900, 400}
        set viewOptions to the icon view options of container window
        set arrangement of viewOptions to not arranged
        set icon size of viewOptions to 72
        make new alias file at container window to POSIX file "/Applications" with properties {name:"Applications"}
        set position of item "'$APP_NAME'.app" of container window to {150, 150}
        set position of item "Applications" of container window to {350, 150}
        close
        open
        update without registering applications
        delay 2
    end tell
end tell' | osascript

# Sincronizar
sync

# Desmontar
hdiutil detach "$DEVICE"

# Converter para DMG final comprimido
hdiutil convert "$TEMP_DMG" -format UDZO -imagekey zlib-level=9 -o "$FINAL_DMG"

# Limpar
rm "$TEMP_DMG"
rm -rf "$APP_BUNDLE"

echo "✅ DMG criado: $FINAL_DMG"
```

### Linux

#### Debian Package

```bash
#!/bin/bash
# packaging/linux/build-deb.sh

set -e

OUTPUT_PATH="$1"
VERSION="1.0.0"
ARCH="amd64"
PACKAGE_NAME="autoinstaller-desktop"

echo "🐧 Criando pacote DEB..."

# Criar estrutura do pacote
PACKAGE_DIR="$OUTPUT_PATH/deb/$PACKAGE_NAME-$VERSION"
mkdir -p "$PACKAGE_DIR/DEBIAN"
mkdir -p "$PACKAGE_DIR/usr/bin"
mkdir -p "$PACKAGE_DIR/usr/share/applications"
mkdir -p "$PACKAGE_DIR/usr/share/icons/hicolor/256x256/apps"
mkdir -p "$PACKAGE_DIR/usr/share/doc/$PACKAGE_NAME"

# Copiar executável
cp "$OUTPUT_PATH/publish/linux-x64/AutoInstaller.UI" "$PACKAGE_DIR/usr/bin/autoinstaller-desktop"
chmod +x "$PACKAGE_DIR/usr/bin/autoinstaller-desktop"

# Criar arquivo de controle
cat > "$PACKAGE_DIR/DEBIAN/control" << EOF
Package: $PACKAGE_NAME
Version: $VERSION
Section: utils
Priority: optional
Architecture: $ARCH
Maintainer: Auto-Instalador Team <<EMAIL>>
Description: Gerenciador de containers Docker e Podman
 Auto-Instalador Desktop é uma aplicação multiplataforma para
 gerenciamento de containers Docker e Podman com interface
 gráfica moderna e intuitiva.
Depends: libc6 (>= 2.31)
Homepage: https://github.com/auto-instalador/desktop
EOF

# Criar arquivo .desktop
cat > "$PACKAGE_DIR/usr/share/applications/autoinstaller-desktop.desktop" << EOF
[Desktop Entry]
Name=Auto-Instalador Desktop
Comment=Gerenciador de containers Docker e Podman
Exec=autoinstaller-desktop
Icon=autoinstaller-desktop
Terminal=false
Type=Application
Categories=Development;System;
StartupWMClass=AutoInstaller.UI
EOF

# Copiar ícone
cp "Assets/icon.png" "$PACKAGE_DIR/usr/share/icons/hicolor/256x256/apps/autoinstaller-desktop.png"

# Criar documentação
cat > "$PACKAGE_DIR/usr/share/doc/$PACKAGE_NAME/README" << EOF
Auto-Instalador Desktop
======================

Gerenciador de containers Docker e Podman com interface gráfica.

Para mais informações, visite:
https://github.com/auto-instalador/desktop
EOF

# Criar changelog
cat > "$PACKAGE_DIR/usr/share/doc/$PACKAGE_NAME/changelog" << EOF
autoinstaller-desktop ($VERSION) stable; urgency=medium

  * Versão inicial do Auto-Instalador Desktop
  * Suporte completo para Docker e Podman
  * Interface moderna com Avalonia UI
  * Arquitetura Clean com CQRS

 -- Auto-Instalador Team <<EMAIL>>  $(date -R)
EOF

# Comprimir changelog
gzip -9 "$PACKAGE_DIR/usr/share/doc/$PACKAGE_NAME/changelog"

# Criar copyright
cat > "$PACKAGE_DIR/usr/share/doc/$PACKAGE_NAME/copyright" << EOF
Format: https://www.debian.org/doc/packaging-manuals/copyright-format/1.0/
Upstream-Name: Auto-Instalador Desktop
Source: https://github.com/auto-instalador/desktop

Files: *
Copyright: 2024 Auto-Instalador Team
License: MIT

License: MIT
 Permission is hereby granted, free of charge, to any person obtaining a
 copy of this software and associated documentation files (the "Software"),
 to deal in the Software without restriction, including without limitation
 the rights to use, copy, modify, merge, publish, distribute, sublicense,
 and/or sell copies of the Software, and to permit persons to whom the
 Software is furnished to do so, subject to the following conditions:
 .
 The above copyright notice and this permission notice shall be included
 in all copies or substantial portions of the Software.
 .
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
 THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 DEALINGS IN THE SOFTWARE.
EOF

# Construir pacote
dpkg-deb --build "$PACKAGE_DIR" "$OUTPUT_PATH/$PACKAGE_NAME-$VERSION-$ARCH.deb"

# Limpar
rm -rf "$PACKAGE_DIR"

echo "✅ Pacote DEB criado: $OUTPUT_PATH/$PACKAGE_NAME-$VERSION-$ARCH.deb"
```

#### AppImage

```bash
#!/bin/bash
# packaging/linux/build-appimage.sh

set -e

OUTPUT_PATH="$1"
VERSION="1.0.0"
APP_NAME="AutoInstaller-Desktop"

echo "🐧 Criando AppImage..."

# Baixar AppImageTool se necessário
if [ ! -f "appimagetool-x86_64.AppImage" ]; then
    wget -O appimagetool-x86_64.AppImage "https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    chmod +x appimagetool-x86_64.AppImage
fi

# Criar estrutura AppDir
APPDIR="$OUTPUT_PATH/$APP_NAME.AppDir"
mkdir -p "$APPDIR/usr/bin"
mkdir -p "$APPDIR/usr/share/applications"
mkdir -p "$APPDIR/usr/share/icons/hicolor/256x256/apps"

# Copiar executável
cp "$OUTPUT_PATH/publish/linux-x64/AutoInstaller.UI" "$APPDIR/usr/bin/autoinstaller-desktop"
chmod +x "$APPDIR/usr/bin/autoinstaller-desktop"

# Criar AppRun
cat > "$APPDIR/AppRun" << 'EOF'
#!/bin/bash
HERE="$(dirname "$(readlink -f "${0}")")"
exec "$HERE/usr/bin/autoinstaller-desktop" "$@"
EOF
chmod +x "$APPDIR/AppRun"

# Criar arquivo .desktop
cat > "$APPDIR/autoinstaller-desktop.desktop" << EOF
[Desktop Entry]
Name=Auto-Instalador Desktop
Comment=Gerenciador de containers Docker e Podman
Exec=autoinstaller-desktop
Icon=autoinstaller-desktop
Terminal=false
Type=Application
Categories=Development;System;
EOF

# Copiar para usr/share/applications também
cp "$APPDIR/autoinstaller-desktop.desktop" "$APPDIR/usr/share/applications/"

# Copiar ícone
cp "Assets/icon.png" "$APPDIR/autoinstaller-desktop.png"
cp "Assets/icon.png" "$APPDIR/usr/share/icons/hicolor/256x256/apps/autoinstaller-desktop.png"

# Criar AppImage
./appimagetool-x86_64.AppImage "$APPDIR" "$OUTPUT_PATH/$APP_NAME-$VERSION-x86_64.AppImage"

# Limpar
rm -rf "$APPDIR"

echo "✅ AppImage criado: $OUTPUT_PATH/$APP_NAME-$VERSION-x86_64.AppImage"
```

---

## Auto-Update

### Sistema de Atualização

#### Update Service

```csharp
// src/AutoInstaller.Infrastructure/Services/UpdateService.cs
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

public class UpdateService : IUpdateService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<UpdateService> _logger;
    private readonly UpdateOptions _options;
    
    public UpdateService(
        HttpClient httpClient,
        ILogger<UpdateService> logger,
        IOptions<UpdateOptions> options)
    {
        _httpClient = httpClient;
        _logger = logger;
        _options = options.Value;
    }
    
    public async Task<UpdateInfo?> CheckForUpdatesAsync()
    {
        try
        {
            _logger.LogInformation("Verificando atualizações...");
            
            var response = await _httpClient.GetAsync(_options.UpdateUrl);
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync();
            var updateInfo = JsonSerializer.Deserialize<UpdateInfo>(json);
            
            if (updateInfo != null && IsNewerVersion(updateInfo.Version))
            {
                _logger.LogInformation("Nova versão disponível: {Version}", updateInfo.Version);
                return updateInfo;
            }
            
            _logger.LogInformation("Nenhuma atualização disponível");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar atualizações");
            return null;
        }
    }
    
    public async Task<bool> DownloadAndInstallUpdateAsync(UpdateInfo updateInfo, IProgress<DownloadProgress>? progress = null)
    {
        try
        {
            _logger.LogInformation("Baixando atualização: {Version}", updateInfo.Version);
            
            var downloadUrl = GetDownloadUrlForCurrentPlatform(updateInfo);
            var tempFile = Path.GetTempFileName();
            
            using var response = await _httpClient.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead);
            response.EnsureSuccessStatusCode();
            
            var totalBytes = response.Content.Headers.ContentLength ?? 0;
            var downloadedBytes = 0L;
            
            using var contentStream = await response.Content.ReadAsStreamAsync();
            using var fileStream = File.Create(tempFile);
            
            var buffer = new byte[8192];
            int bytesRead;
            
            while ((bytesRead = await contentStream.ReadAsync(buffer)) > 0)
            {
                await fileStream.WriteAsync(buffer.AsMemory(0, bytesRead));
                downloadedBytes += bytesRead;
                
                progress?.Report(new DownloadProgress
                {
                    BytesDownloaded = downloadedBytes,
                    TotalBytes = totalBytes,
                    ProgressPercentage = totalBytes > 0 ? (double)downloadedBytes / totalBytes * 100 : 0
                });
            }
            
            _logger.LogInformation("Download concluído. Iniciando instalação...");
            
            // Verificar assinatura digital
            if (!await VerifySignatureAsync(tempFile, updateInfo.Signature))
            {
                _logger.LogError("Falha na verificação da assinatura digital");
                File.Delete(tempFile);
                return false;
            }
            
            // Instalar atualização
            return await InstallUpdateAsync(tempFile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao baixar/instalar atualização");
            return false;
        }
    }
    
    private bool IsNewerVersion(string newVersion)
    {
        var currentVersion = Version.Parse(GetCurrentVersion());
        var availableVersion = Version.Parse(newVersion);
        
        return availableVersion > currentVersion;
    }
    
    private string GetCurrentVersion()
    {
        var assembly = Assembly.GetExecutingAssembly();
        return assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion ?? "1.0.0";
    }
    
    private string GetDownloadUrlForCurrentPlatform(UpdateInfo updateInfo)
    {
        var platform = Environment.OSVersion.Platform switch
        {
            PlatformID.Win32NT => "windows",
            PlatformID.Unix when RuntimeInformation.IsOSPlatform(OSPlatform.OSX) => "macos",
            PlatformID.Unix => "linux",
            _ => throw new PlatformNotSupportedException()
        };
        
        var architecture = RuntimeInformation.ProcessArchitecture switch
        {
            Architecture.X64 => "x64",
            Architecture.Arm64 => "arm64",
            _ => throw new PlatformNotSupportedException()
        };
        
        return updateInfo.Downloads.FirstOrDefault(d => 
            d.Platform == platform && d.Architecture == architecture)?.Url 
            ?? throw new PlatformNotSupportedException($"Plataforma não suportada: {platform}-{architecture}");
    }
    
    private async Task<bool> VerifySignatureAsync(string filePath, string expectedSignature)
    {
        // Implementar verificação de assinatura digital
        // Por exemplo, usando SHA256 + RSA
        
        using var sha256 = SHA256.Create();
        using var fileStream = File.OpenRead(filePath);
        var hash = await sha256.ComputeHashAsync(fileStream);
        var hashString = Convert.ToHexString(hash);
        
        return string.Equals(hashString, expectedSignature, StringComparison.OrdinalIgnoreCase);
    }
    
    private async Task<bool> InstallUpdateAsync(string updateFilePath)
    {
        try
        {
            var currentExecutable = Environment.ProcessPath ?? throw new InvalidOperationException("Não foi possível determinar o executável atual");
            var backupPath = currentExecutable + ".backup";
            var tempUpdatePath = currentExecutable + ".update";
            
            // Copiar atualização para local temporário
            File.Copy(updateFilePath, tempUpdatePath, true);
            
            // Criar script de atualização
            var updateScript = CreateUpdateScript(currentExecutable, tempUpdatePath, backupPath);
            
            // Executar script e sair da aplicação
            var processInfo = new ProcessStartInfo
            {
                FileName = updateScript,
                UseShellExecute = true,
                CreateNoWindow = true
            };
            
            Process.Start(processInfo);
            
            // Sinalizar para a aplicação que deve ser fechada
            Environment.Exit(0);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao instalar atualização");
            return false;
        }
    }
    
    private string CreateUpdateScript(string currentExe, string updateExe, string backupExe)
    {
        var scriptPath = Path.GetTempFileName();
        
        if (Environment.OSVersion.Platform == PlatformID.Win32NT)
        {
            // Script PowerShell para Windows
            scriptPath += ".ps1";
            var script = $@"
Start-Sleep -Seconds 2
Move-Item '{currentExe}' '{backupExe}' -Force
Move-Item '{updateExe}' '{currentExe}' -Force
Start-Process '{currentExe}'
Remove-Item '{backupExe}' -Force -ErrorAction SilentlyContinue
Remove-Item $PSCommandPath -Force
";
            File.WriteAllText(scriptPath, script);
        }
        else
        {
            // Script Bash para Unix
            scriptPath += ".sh";
            var script = $@"#!/bin/bash
sleep 2
mv '{currentExe}' '{backupExe}'
mv '{updateExe}' '{currentExe}'
chmod +x '{currentExe}'
'{currentExe}' &
rm -f '{backupExe}'
rm -f $0
";
            File.WriteAllText(scriptPath, script);
            
            // Tornar executável
            var chmodInfo = new ProcessStartInfo("chmod", $"+x {scriptPath}")
            {
                UseShellExecute = false,
                CreateNoWindow = true
            };
            Process.Start(chmodInfo)?.WaitForExit();
        }
        
        return scriptPath;
    }
}

// Models
public class UpdateInfo
{
    public string Version { get; set; } = string.Empty;
    public string ReleaseNotes { get; set; } = string.Empty;
    public DateTime ReleaseDate { get; set; }
    public string Signature { get; set; } = string.Empty;
    public List<DownloadInfo> Downloads { get; set; } = new();
}

public class DownloadInfo
{
    public string Platform { get; set; } = string.Empty;
    public string Architecture { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public long Size { get; set; }
}

public class DownloadProgress
{
    public long BytesDownloaded { get; set; }
    public long TotalBytes { get; set; }
    public double ProgressPercentage { get; set; }
}

public class UpdateOptions
{
    public string UpdateUrl { get; set; } = string.Empty;
    public bool AutoCheck { get; set; } = true;
    public TimeSpan CheckInterval { get; set; } = TimeSpan.FromHours(24);
    public bool AutoDownload { get; set; } = false;
    public bool AutoInstall { get; set; } = false;
}
```

### Update Manifest

```json
{
  "version": "1.0.1",
  "releaseDate": "2024-01-15T10:00:00Z",
  "releaseNotes": "Correções de bugs e melhorias de performance",
  "signature": "SHA256_HASH_HERE",
  "downloads": [
    {
      "platform": "windows",
      "architecture": "x64",
      "url": "https://releases.autoinstalador.com/v1.0.1/AutoInstaller-Desktop-1.0.1-win-x64.msi",
      "size": 52428800
    },
    {
      "platform": "windows",
      "architecture": "arm64",
      "url": "https://releases.autoinstalador.com/v1.0.1/AutoInstaller-Desktop-1.0.1-win-arm64.msi",
      "size": 48234496
    },
    {
      "platform": "macos",
      "architecture": "x64",
      "url": "https://releases.autoinstalador.com/v1.0.1/AutoInstaller-Desktop-1.0.1-osx-x64.dmg",
      "size": 45678912
    },
    {
      "platform": "macos",
      "architecture": "arm64",
      "url": "https://releases.autoinstalador.com/v1.0.1/AutoInstaller-Desktop-1.0.1-osx-arm64.dmg",
      "size": 43521024
    },
    {
      "platform": "linux",
      "architecture": "x64",
      "url": "https://releases.autoinstalador.com/v1.0.1/AutoInstaller-Desktop-1.0.1-linux-x64.AppImage",
      "size": 67108864
    },
    {
      "platform": "linux",
      "architecture": "arm64",
      "url": "https://releases.autoinstalador.com/v1.0.1/AutoInstaller-Desktop-1.0.1-linux-arm64.AppImage",
      "size": 62914560
    }
  ]
}
```

---

## CI/CD Pipeline

### GitHub Actions

```yaml
# .github/workflows/build-and-deploy.yml
name: Build and Deploy

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

env:
  DOTNET_VERSION: '9.0.x'
  SOLUTION_PATH: 'src/AutoInstaller.sln'
  ARTIFACTS_PATH: 'artifacts'

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
        
    - name: Restore dependencies
      run: dotnet restore ${{ env.SOLUTION_PATH }}
      
    - name: Build
      run: dotnet build ${{ env.SOLUTION_PATH }} --configuration Release --no-restore
      
    - name: Test
      run: |
        dotnet test ${{ env.SOLUTION_PATH }} \
          --configuration Release \
          --no-build \
          --collect:"XPlat Code Coverage" \
          --logger trx \
          --results-directory TestResults/
          
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: TestResults/
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: TestResults/**/coverage.cobertura.xml
        fail_ci_if_error: true

  build:
    name: Build
    needs: test
    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
        include:
          - os: windows-latest
            runtime: win-x64
            artifact-name: windows-x64
          - os: ubuntu-latest
            runtime: linux-x64
            artifact-name: linux-x64
          - os: macos-latest
            runtime: osx-x64
            artifact-name: macos-x64
    
    runs-on: ${{ matrix.os }}
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
        
    - name: Restore dependencies
      run: dotnet restore ${{ env.SOLUTION_PATH }}
      
    - name: Publish
      run: |
        dotnet publish src/AutoInstaller.UI/AutoInstaller.UI.csproj \
          --configuration Release \
          --runtime ${{ matrix.runtime }} \
          --self-contained true \
          --output ${{ env.ARTIFACTS_PATH }}/publish/${{ matrix.runtime }} \
          /p:PublishSingleFile=true \
          /p:PublishReadyToRun=true \
          /p:PublishTrimmed=true
          
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-${{ matrix.artifact-name }}
        path: ${{ env.ARTIFACTS_PATH }}/publish/${{ matrix.runtime }}

  package:
    name: Package
    needs: build
    if: startsWith(github.ref, 'refs/tags/v')
    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
        include:
          - os: windows-latest
            package-script: packaging/windows/build-msi.ps1
            artifact-pattern: '*.msi'
            artifact-name: windows-packages
          - os: ubuntu-latest
            package-script: packaging/linux/build-packages.sh
            artifact-pattern: '*.{deb,rpm,AppImage}'
            artifact-name: linux-packages
          - os: macos-latest
            package-script: packaging/macos/build-dmg.sh
            artifact-pattern: '*.dmg'
            artifact-name: macos-packages
    
    runs-on: ${{ matrix.os }}
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        pattern: build-*
        path: ${{ env.ARTIFACTS_PATH }}/publish
        merge-multiple: true
        
    - name: Install packaging tools (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        # Instalar WiX Toolset
        choco install wixtoolset
        
    - name: Install packaging tools (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y rpm
        
    - name: Create packages
      run: ${{ matrix.package-script }} ${{ env.ARTIFACTS_PATH }}
      
    - name: Upload packages
      uses: actions/upload-artifact@v4
      with:
        name: ${{ matrix.artifact-name }}
        path: ${{ env.ARTIFACTS_PATH }}/${{ matrix.artifact-pattern }}

  release:
    name: Release
    needs: [test, build, package]
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      with:
        path: ${{ env.ARTIFACTS_PATH }}
        
    - name: Create release
      uses: softprops/action-gh-release@v1
      with:
        files: ${{ env.ARTIFACTS_PATH }}/**/*
        generate_release_notes: true
        draft: false
        prerelease: ${{ contains(github.ref, '-') }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Update release manifest
      run: |
        # Gerar manifest de atualização
        python scripts/generate-update-manifest.py \
          --version ${{ github.ref_name }} \
          --artifacts-path ${{ env.ARTIFACTS_PATH }} \
          --output update-manifest.json
          
    - name: Deploy update manifest
      run: |
        # Deploy do manifest para CDN/servidor de atualizações
        # Implementar conforme infraestrutura escolhida
        echo "Deploying update manifest..."
```

---

## Monitoramento

### Application Insights

```csharp
// src/AutoInstaller.Infrastructure/Telemetry/TelemetryService.cs
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Extensibility;

public class TelemetryService : ITelemetryService
{
    private readonly TelemetryClient _telemetryClient;
    private readonly ILogger<TelemetryService> _logger;
    
    public TelemetryService(TelemetryClient telemetryClient, ILogger<TelemetryService> logger)
    {
        _telemetryClient = telemetryClient;
        _logger = logger;
    }
    
    public void TrackEvent(string eventName, IDictionary<string, string>? properties = null, IDictionary<string, double>? metrics = null)
    {
        try
        {
            _telemetryClient.TrackEvent(eventName, properties, metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar métrica para telemetria");
        }
    }
    
    public void TrackDependency(string dependencyTypeName, string dependencyName, string data, DateTimeOffset startTime, TimeSpan duration, bool success)
    {
        try
        {
            _telemetryClient.TrackDependency(dependencyTypeName, dependencyName, data, startTime, duration, success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar dependência para telemetria");
        }
    }
    
    public void Flush()
    {
        _telemetryClient.Flush();
    }
}
```

### Health Checks

```csharp
// src/AutoInstaller.Infrastructure/HealthChecks/ContainerEngineHealthCheck.cs
using Microsoft.Extensions.Diagnostics.HealthChecks;

public class ContainerEngineHealthCheck : IHealthCheck
{
    private readonly IContainerEngineFactory _containerEngineFactory;
    private readonly ILogger<ContainerEngineHealthCheck> _logger;
    
    public ContainerEngineHealthCheck(
        IContainerEngineFactory containerEngineFactory,
        ILogger<ContainerEngineHealthCheck> logger)
    {
        _containerEngineFactory = containerEngineFactory;
        _logger = logger;
    }
    
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var containerEngine = await _containerEngineFactory.CreateAsync();
            
            // Verificar se o engine está respondendo
            var version = await containerEngine.GetVersionAsync();
            
            var data = new Dictionary<string, object>
            {
                ["engine_type"] = containerEngine.GetType().Name,
                ["version"] = version,
                ["timestamp"] = DateTimeOffset.UtcNow
            };
            
            return HealthCheckResult.Healthy("Container engine está funcionando", data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Falha no health check do container engine");
            return HealthCheckResult.Unhealthy("Container engine não está disponível", ex);
        }
    }
}
```

---

## Rollback e Versionamento

### Estratégia de Rollback

#### Rollback Automático

```csharp
// src/AutoInstaller.Infrastructure/Services/RollbackService.cs
public class RollbackService : IRollbackService
{
    private readonly ILogger<RollbackService> _logger;
    private readonly IFileSystem _fileSystem;
    
    public RollbackService(ILogger<RollbackService> logger, IFileSystem fileSystem)
    {
        _logger = logger;
        _fileSystem = fileSystem;
    }
    
    public async Task<bool> CreateBackupAsync(string version)
    {
        try
        {
            var currentExecutable = Environment.ProcessPath ?? throw new InvalidOperationException();
            var backupDirectory = Path.Combine(Path.GetDirectoryName(currentExecutable)!, "backups", version);
            
            _fileSystem.Directory.CreateDirectory(backupDirectory);
            
            // Backup do executável
            var backupExecutable = Path.Combine(backupDirectory, Path.GetFileName(currentExecutable));
            _fileSystem.File.Copy(currentExecutable, backupExecutable, true);
            
            // Backup das configurações
            var configFiles = new[] { "appsettings.json", "appsettings.Production.json" };
            var appDirectory = Path.GetDirectoryName(currentExecutable)!;
            
            foreach (var configFile in configFiles)
            {
                var sourcePath = Path.Combine(appDirectory, configFile);
                if (_fileSystem.File.Exists(sourcePath))
                {
                    var backupPath = Path.Combine(backupDirectory, configFile);
                    _fileSystem.File.Copy(sourcePath, backupPath, true);
                }
            }
            
            _logger.LogInformation("Backup criado para versão {Version} em {BackupDirectory}", version, backupDirectory);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao criar backup para versão {Version}", version);
            return false;
        }
    }
    
    public async Task<bool> RollbackToVersionAsync(string version)
    {
        try
        {
            var currentExecutable = Environment.ProcessPath ?? throw new InvalidOperationException();
            var backupDirectory = Path.Combine(Path.GetDirectoryName(currentExecutable)!, "backups", version);
            
            if (!_fileSystem.Directory.Exists(backupDirectory))
            {
                _logger.LogError("Backup não encontrado para versão {Version}", version);
                return false;
            }
            
            // Criar backup da versão atual antes do rollback
            var currentVersion = GetCurrentVersion();
            await CreateBackupAsync($"{currentVersion}-pre-rollback");
            
            // Restaurar executável
            var backupExecutable = Path.Combine(backupDirectory, Path.GetFileName(currentExecutable));
            if (_fileSystem.File.Exists(backupExecutable))
            {
                _fileSystem.File.Copy(backupExecutable, currentExecutable, true);
            }
            
            // Restaurar configurações
            var configFiles = _fileSystem.Directory.GetFiles(backupDirectory, "appsettings*.json");
            var appDirectory = Path.GetDirectoryName(currentExecutable)!;
            
            foreach (var configFile in configFiles)
            {
                var fileName = Path.GetFileName(configFile);
                var targetPath = Path.Combine(appDirectory, fileName);
                _fileSystem.File.Copy(configFile, targetPath, true);
            }
            
            _logger.LogInformation("Rollback concluído para versão {Version}", version);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao fazer rollback para versão {Version}", version);
            return false;
        }
    }
    
    public Task<List<string>> GetAvailableBackupsAsync()
    {
        try
        {
            var currentExecutable = Environment.ProcessPath ?? throw new InvalidOperationException();
            var backupsDirectory = Path.Combine(Path.GetDirectoryName(currentExecutable)!, "backups");
            
            if (!_fileSystem.Directory.Exists(backupsDirectory))
            {
                return Task.FromResult(new List<string>());
            }
            
            var backups = _fileSystem.Directory.GetDirectories(backupsDirectory)
                .Select(Path.GetFileName)
                .Where(name => !string.IsNullOrEmpty(name))
                .Cast<string>()
                .OrderByDescending(name => name)
                .ToList();
            
            return Task.FromResult(backups);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao listar backups disponíveis");
            return Task.FromResult(new List<string>());
        }
    }
    
    private string GetCurrentVersion()
    {
        var assembly = Assembly.GetExecutingAssembly();
        return assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion ?? "unknown";
    }
}
```

### Versionamento de Banco de Dados

```csharp
// src/AutoInstaller.Infrastructure/Migrations/MigrationService.cs
public class MigrationService : IMigrationService
{
    private readonly AutoInstallerDbContext _context;
    private readonly ILogger<MigrationService> _logger;
    
    public MigrationService(AutoInstallerDbContext context, ILogger<MigrationService> logger)
    {
        _context = context;
        _logger = logger;
    }
    
    public async Task<bool> MigrateAsync()
    {
        try
        {
            _logger.LogInformation("Iniciando migração do banco de dados...");
            
            // Criar backup antes da migração
            await CreateDatabaseBackupAsync();
            
            // Aplicar migrações
            await _context.Database.MigrateAsync();
            
            _logger.LogInformation("Migração do banco de dados concluída com sucesso");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro durante migração do banco de dados");
            
            // Tentar rollback automático
            await RollbackDatabaseAsync();
            return false;
        }
    }
    
    private async Task CreateDatabaseBackupAsync()
    {
        var connectionString = _context.Database.GetConnectionString();
        var backupPath = Path.Combine("backups", $"database-{DateTime.UtcNow:yyyyMMdd-HHmmss}.db");
        
        // Implementar backup específico por provider (SQLite, SQL Server, etc.)
        if (connectionString?.Contains(".db") == true)
        {
            // SQLite backup
            var sourceDb = connectionString.Split('=')[1].Split(';')[0];
            File.Copy(sourceDb, backupPath, true);
            _logger.LogInformation("Backup do banco SQLite criado: {BackupPath}", backupPath);
        }
    }
    
    private async Task RollbackDatabaseAsync()
    {
        _logger.LogWarning("Iniciando rollback do banco de dados...");
        
        // Implementar rollback específico por provider
        // Para SQLite, restaurar do backup mais recente
        // Para SQL Server, usar transações ou backups
        
        _logger.LogInformation("Rollback do banco de dados concluído");
    }
}
```

---

## Configurações de Ambiente

### Configuração por Ambiente

#### appsettings.json

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=autoinstaller.db"
  },
  "ContainerEngine": {
    "PreferredEngine": "Auto",
    "DockerEndpoint": "npipe://./pipe/docker_engine",
    "PodmanEndpoint": "unix:///run/user/1000/podman/podman.sock",
    "ConnectionTimeout": "00:00:30",
    "OperationTimeout": "00:05:00"
  },
  "Update": {
    "UpdateUrl": "https://api.autoinstalador.com/updates/manifest.json",
    "AutoCheck": true,
    "CheckInterval": "1.00:00:00",
    "AutoDownload": false,
    "AutoInstall": false
  },
  "Telemetry": {
    "Enabled": true,
    "InstrumentationKey": "",
    "SamplingPercentage": 100.0
  },
  "UI": {
    "Theme": "System",
    "Language": "pt-BR",
    "StartMinimized": false,
    "MinimizeToTray": true,
    "CheckForUpdatesOnStartup": true
  }
}
```

#### appsettings.Development.json

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "AutoInstaller": "Debug",
      "Microsoft": "Information"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=autoinstaller-dev.db"
  },
  "ContainerEngine": {
    "OperationTimeout": "00:01:00"
  },
  "Update": {
    "UpdateUrl": "https://api-dev.autoinstalador.com/updates/manifest.json",
    "AutoCheck": false
  },
  "Telemetry": {
    "Enabled": false
  }
}
```

#### appsettings.Production.json

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "AutoInstaller": "Information",
      "Microsoft": "Warning"
    }
  },
  "Telemetry": {
    "Enabled": true,
    "SamplingPercentage": 10.0
  },
  "Update": {
    "AutoCheck": true,
    "CheckInterval": "1.00:00:00"
  }
}
```

### Configuração de Logging

#### Serilog Configuration

```json
{
  "Serilog": {
    "Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.ApplicationInsights"],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/autoinstaller-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30,
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "ApplicationInsights",
        "Args": {
          "telemetryConverter": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"
        }
      }
    ],
    "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]
  }
}
```

---

## Troubleshooting

### Problemas Comuns

#### 1. Falha na Detecção do Container Engine

**Sintomas:**
- Aplicação não consegue detectar Docker ou Podman
- Erro: "Nenhum container engine disponível"

**Soluções:**
```bash
# Verificar se Docker está rodando
docker version

# Verificar se Podman está instalado
podman version

# Windows: Verificar se Docker Desktop está iniciado
# Linux: Verificar se o serviço está ativo
sudo systemctl status docker
sudo systemctl status podman

# Verificar permissões (Linux)
sudo usermod -aG docker $USER
# Logout e login novamente
```

#### 2. Problemas de Conectividade

**Sintomas:**
- Timeout ao conectar com container engine
- Erro: "Connection refused"

**Soluções:**
```json
// Ajustar timeouts em appsettings.json
{
  "ContainerEngine": {
    "ConnectionTimeout": "00:01:00",
    "OperationTimeout": "00:10:00"
  }
}
```

#### 3. Falha na Atualização

**Sintomas:**
- Download de atualização falha
- Erro de verificação de assinatura

**Soluções:**
```bash
# Verificar conectividade
curl -I https://api.autoinstalador.com/updates/manifest.json

# Limpar cache de atualizações
rm -rf ~/.autoinstaller/updates/

# Verificar espaço em disco
df -h
```

#### 4. Problemas de Performance

**Sintomas:**
- Interface lenta
- Alto uso de CPU/memória

**Soluções:**
```json
// Ajustar configurações de UI
{
  "UI": {
    "RefreshInterval": "00:00:05",
    "MaxContainersPerPage": 50,
    "EnableAnimations": false
  }
}
```

### Logs de Diagnóstico

#### Script de Coleta de Logs

```powershell
# collect-diagnostics.ps1
param(
    [string]$OutputPath = "./diagnostics"
)

$ErrorActionPreference = "Continue"

Write-Host "🔍 Coletando informações de diagnóstico..." -ForegroundColor Green

# Criar diretório de saída
New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null

# Informações do sistema
Write-Host "📋 Coletando informações do sistema..."
Get-ComputerInfo | Out-File "$OutputPath/system-info.txt"
Get-Process | Where-Object { $_.ProcessName -like "*docker*" -or $_.ProcessName -like "*podman*" } | Out-File "$OutputPath/container-processes.txt"

# Logs da aplicação
Write-Host "📄 Coletando logs da aplicação..."
$logPath = "$env:LOCALAPPDATA/AutoInstaller/logs"
if (Test-Path $logPath) {
    Copy-Item -Path "$logPath/*" -Destination "$OutputPath/" -Recurse -Force
}

# Configurações
Write-Host "⚙️ Coletando configurações..."
$configPath = "$env:LOCALAPPDATA/AutoInstaller"
if (Test-Path "$configPath/appsettings.json") {
    Copy-Item -Path "$configPath/appsettings.json" -Destination "$OutputPath/"
}

# Informações do Docker
Write-Host "🐳 Coletando informações do Docker..."
try {
    docker version 2>&1 | Out-File "$OutputPath/docker-version.txt"
    docker info 2>&1 | Out-File "$OutputPath/docker-info.txt"
    docker ps -a 2>&1 | Out-File "$OutputPath/docker-containers.txt"
} catch {
    "Docker não disponível" | Out-File "$OutputPath/docker-error.txt"
}

# Informações do Podman
Write-Host "🦭 Coletando informações do Podman..."
try {
    podman version 2>&1 | Out-File "$OutputPath/podman-version.txt"
    podman info 2>&1 | Out-File "$OutputPath/podman-info.txt"
    podman ps -a 2>&1 | Out-File "$OutputPath/podman-containers.txt"
} catch {
    "Podman não disponível" | Out-File "$OutputPath/podman-error.txt"
}

# Criar arquivo ZIP
Write-Host "📦 Criando arquivo de diagnóstico..."
$zipPath = "$OutputPath/../diagnostics-$(Get-Date -Format 'yyyyMMdd-HHmmss').zip"
Compress-Archive -Path "$OutputPath/*" -DestinationPath $zipPath -Force

Write-Host "✅ Diagnóstico concluído: $zipPath" -ForegroundColor Green
Write-Host "📧 Envie este arquivo para o suporte técnico" -ForegroundColor Yellow
```

### Ferramentas de Debug

#### Debug Mode

```csharp
// src/AutoInstaller.UI/Services/DebugService.cs
public class DebugService : IDebugService
{
    private readonly ILogger<DebugService> _logger;
    private readonly ITelemetryService _telemetry;
    
    public bool IsDebugMode { get; private set; }
    
    public DebugService(ILogger<DebugService> logger, ITelemetryService telemetry)
    {
        _logger = logger;
        _telemetry = telemetry;
        
#if DEBUG
        IsDebugMode = true;
#else
        IsDebugMode = Environment.GetEnvironmentVariable("AUTOINSTALLER_DEBUG") == "1";
#endif
    }
    
    public void EnableDebugMode()
    {
        IsDebugMode = true;
        _logger.LogInformation("Modo debug ativado");
        _telemetry.TrackEvent("DebugModeEnabled");
    }
    
    public void DisableDebugMode()
    {
        IsDebugMode = false;
        _logger.LogInformation("Modo debug desativado");
        _telemetry.TrackEvent("DebugModeDisabled");
    }
    
    public void LogDebug(string message, object? data = null)
    {
        if (IsDebugMode)
        {
            _logger.LogDebug("[DEBUG] {Message} {Data}", message, data);
        }
    }
}
```

---

## Conclusão

Este documento fornece uma estratégia completa de deployment e distribuição para o Auto-Instalador Desktop, cobrindo:

- **Build multiplataforma** com .NET 9
- **Empacotamento específico** para Windows, macOS e Linux
- **Sistema de auto-update** seguro e confiável
- **CI/CD pipeline** automatizado com GitHub Actions
- **Monitoramento e telemetria** em produção
- **Estratégias de rollback** para recuperação rápida
- **Troubleshooting** para problemas comuns

A implementação dessas práticas garante que o Auto-Instalador Desktop seja distribuído de forma profissional, segura e confiável em todas as plataformas suportadas.

**Próximos Passos:**
1. Configurar infraestrutura de CI/CD
2. Implementar sistema de telemetria
3. Configurar CDN para distribuição
4. Estabelecer processo de release
5. Documentar procedimentos operacionais telemetria: {EventName}", eventName);
        }
    }
    
    public void TrackException(Exception exception, IDictionary<string, string>? properties = null)
    {
        try
        {
            _telemetryClient.TrackException(exception, properties);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar exceção para telemetria");
        }
    }
    
    public void TrackMetric(string metricName, double value, IDictionary<string, string>? properties = null)
    {
        try
        {
            _telemetryClient.TrackMetric(metricName, value, properties);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar