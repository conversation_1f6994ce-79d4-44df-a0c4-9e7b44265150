# Arquitetura Modular - Auto-Instalador Desktop Multiplataforma Autônomo

## Visão Geral
Este documento define a arquitetura modular do **Auto-Instalador Desktop Multiplataforma Autônomo**, estabelecendo a estrutura de camadas para sistema com engines embarcadas, padrões de projeto para Docker.DotNet e PodManClient.DotNet, sistema de componentização plugável e separação clara de responsabilidades entre os 7 agentes especializados.

---

## 1. Estrutura de Camadas (Clean Architecture)

### 1.1 Visão Geral das Camadas - Sistema Autônomo

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           Presentation Layer                                   │
│                        (AutoInstaller.UI)                                      │
│                    Interface Docker Desktop Dark/Blue                          │
├─────────────────────────────────────────────────────────────────────────────────┤
│                          Application Layer                                     │
│                     (AutoInstaller.Application)                               │
│                      CQRS + MediatR + 7 Agentes                               │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            Domain Layer                                        │
│                       (AutoInstaller.Core)                                    │
│                   Entidades + Interfaces + Regras                             │
├─────────────────────────────────────────────────────────────────────────────────┤
│                        Infrastructure Layer                                    │
│                    (AutoInstaller.Infrastructure)                             │
│                    EF Core + Serilog + Configurações                          │
├─────────────────────────────────────────────────────────────────────────────────┤
│                         Engine Management Layer                                │
│    ┌─────────────────────┬─────────────────────┬─────────────────────────────┐ │
│    │  EngineManager      │   Docker Module     │     Podman Module           │ │
│    │  (Coordenação)      │  (Docker.DotNet)    │  (PodManClient.DotNet)      │ │
│    └─────────────────────┴─────────────────────┴─────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                          Fallback System Layer                                 │
│                     (AutoInstaller.FallbackSystem)                            │
│                   Detecção + Escolha + Configuração                           │
├─────────────────────────────────────────────────────────────────────────────────┤
│                            Shared Layer                                        │
│                       (AutoInstaller.Shared)                                  │
│                      DTOs + Extensions + Helpers                              │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 1.2 Novos Módulos para Sistema Autônomo

#### 1.2.1 AutoInstaller.EngineManager
- **Responsabilidade**: Coordenação de engines embarcadas
- **Funcionalidades**:
  - Detecção automática de sistema operacional
  - Extração de binários apropriados
  - Inicialização e gerenciamento de processos
  - Monitoramento de status das engines

#### 1.2.2 AutoInstaller.Docker
- **Responsabilidade**: Integração com Docker Engine embarcado
- **Funcionalidades**:
  - Comunicação via Docker.DotNet (nunca CLI)
  - Gerenciamento de containers, imagens, volumes
  - Configuração de sockets e named pipes
  - Health checks e monitoramento

#### 1.2.3 AutoInstaller.Podman
- **Responsabilidade**: Integração com Podman Engine embarcado
- **Funcionalidades**:
  - Comunicação via PodManClient.DotNet (nunca CLI)
  - Gerenciamento rootless de containers
  - Suporte a pods nativos
  - Configuração de sockets Unix

#### 1.2.4 AutoInstaller.FallbackSystem
- **Responsabilidade**: Sistema inteligente de fallback
- **Funcionalidades**:
  - Detecção de engines existentes no sistema
  - Apresentação de opções ao usuário
  - Configuração híbrida (existente + embarcada)
  - Recuperação automática em caso de falhas

### 1.2 Dependências entre Camadas

```mermaid
graph TD
    UI[AutoInstaller.UI] --> App[AutoInstaller.Application]
    UI --> Shared[AutoInstaller.Shared]
    App --> Core[AutoInstaller.Core]
    App --> Shared
    Infra[AutoInstaller.Infrastructure] --> Core
    Infra --> Shared
    Core --> Shared
```

---

## 2. Camada de Domínio (AutoInstaller.Core)

### 2.1 Responsabilidades
- Entidades de negócio
- Regras de negócio fundamentais
- Interfaces de repositórios
- Serviços de domínio
- Value Objects
- Exceções de domínio

### 2.2 Estrutura de Pastas

```
AutoInstaller.Core/
├── Entities/
│   ├── Container.cs
│   ├── Image.cs
│   ├── Network.cs
│   ├── Volume.cs
│   ├── ComposeProject.cs
│   └── ApplicationTemplate.cs
├── ValueObjects/
│   ├── ContainerId.cs
│   ├── ImageTag.cs
│   ├── PortMapping.cs
│   ├── EnvironmentVariable.cs
│   └── VolumeMount.cs
├── Interfaces/
│   ├── Repositories/
│   │   ├── IContainerRepository.cs
│   │   ├── IImageRepository.cs
│   │   ├── INetworkRepository.cs
│   │   └── IVolumeRepository.cs
│   ├── Services/
│   │   ├── IContainerEngine.cs
│   │   ├── IImageService.cs
│   │   └── INetworkService.cs
│   └── External/
│       └── IDockerClient.cs
├── Services/
│   ├── ContainerDomainService.cs
│   ├── ImageDomainService.cs
│   └── NetworkDomainService.cs
├── Exceptions/
│   ├── DomainException.cs
│   ├── ContainerNotFoundException.cs
│   ├── ImageNotFoundException.cs
│   └── InvalidConfigurationException.cs
└── Extensions/
    ├── ContainerExtensions.cs
    └── ImageExtensions.cs
```

### 2.3 Exemplo de Entidade

```csharp
using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Shared.Enums;

namespace AutoInstaller.Core.Entities;

public class Container
{
    public ContainerId Id { get; private set; }
    public string Name { get; private set; }
    public ImageTag Image { get; private set; }
    public ContainerStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? StartedAt { get; private set; }
    public IReadOnlyList<PortMapping> PortMappings { get; private set; }
    public IReadOnlyList<VolumeMount> VolumeMounts { get; private set; }
    public IReadOnlyList<EnvironmentVariable> Environment { get; private set; }
    
    private readonly List<PortMapping> _portMappings = new();
    private readonly List<VolumeMount> _volumeMounts = new();
    private readonly List<EnvironmentVariable> _environment = new();
    
    public Container(ContainerId id, string name, ImageTag image)
    {
        Id = id ?? throw new ArgumentNullException(nameof(id));
        Name = !string.IsNullOrWhiteSpace(name) ? name : throw new ArgumentException("Name cannot be empty", nameof(name));
        Image = image ?? throw new ArgumentNullException(nameof(image));
        Status = ContainerStatus.Created;
        CreatedAt = DateTime.UtcNow;
        
        PortMappings = _portMappings.AsReadOnly();
        VolumeMounts = _volumeMounts.AsReadOnly();
        Environment = _environment.AsReadOnly();
    }
    
    public void Start()
    {
        if (Status == ContainerStatus.Running)
            throw new InvalidOperationException("Container is already running");
            
        Status = ContainerStatus.Running;
        StartedAt = DateTime.UtcNow;
    }
    
    public void Stop()
    {
        if (Status != ContainerStatus.Running)
            throw new InvalidOperationException("Container is not running");
            
        Status = ContainerStatus.Stopped;
    }
    
    public void AddPortMapping(PortMapping portMapping)
    {
        if (_portMappings.Any(p => p.HostPort == portMapping.HostPort))
            throw new InvalidOperationException($"Host port {portMapping.HostPort} is already mapped");
            
        _portMappings.Add(portMapping);
    }
    
    public void AddVolumeMount(VolumeMount volumeMount)
    {
        if (_volumeMounts.Any(v => v.ContainerPath == volumeMount.ContainerPath))
            throw new InvalidOperationException($"Container path {volumeMount.ContainerPath} is already mounted");
            
        _volumeMounts.Add(volumeMount);
    }
    
    public void AddEnvironmentVariable(EnvironmentVariable envVar)
    {
        var existing = _environment.FirstOrDefault(e => e.Name == envVar.Name);
        if (existing != null)
        {
            _environment.Remove(existing);
        }
        
        _environment.Add(envVar);
    }
}
```

### 2.4 Exemplo de Value Object

```csharp
namespace AutoInstaller.Core.ValueObjects;

public record ContainerId
{
    public string Value { get; }
    
    public ContainerId(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Container ID cannot be empty", nameof(value));
            
        if (value.Length < 12)
            throw new ArgumentException("Container ID must be at least 12 characters", nameof(value));
            
        Value = value;
    }
    
    public static implicit operator string(ContainerId containerId) => containerId.Value;
    public static implicit operator ContainerId(string value) => new(value);
    
    public override string ToString() => Value;
}
```

---

## 3. Camada de Aplicação (AutoInstaller.Application)

### 3.1 Responsabilidades
- Casos de uso da aplicação
- Comandos e Queries (CQRS)
- Handlers de comandos e queries
- DTOs (Data Transfer Objects)
- Validadores
- Mapeadores
- Serviços de aplicação

### 3.2 Estrutura de Pastas

```
AutoInstaller.Application/
├── Commands/
│   ├── Containers/
│   │   ├── CreateContainerCommand.cs
│   │   ├── StartContainerCommand.cs
│   │   ├── StopContainerCommand.cs
│   │   └── RemoveContainerCommand.cs
│   ├── Images/
│   │   ├── PullImageCommand.cs
│   │   ├── RemoveImageCommand.cs
│   │   └── BuildImageCommand.cs
│   └── Networks/
│       ├── CreateNetworkCommand.cs
│       └── RemoveNetworkCommand.cs
├── Queries/
│   ├── Containers/
│   │   ├── GetContainersQuery.cs
│   │   ├── GetContainerByIdQuery.cs
│   │   └── GetContainerLogsQuery.cs
│   ├── Images/
│   │   ├── GetImagesQuery.cs
│   │   └── GetImageByIdQuery.cs
│   └── Networks/
│       └── GetNetworksQuery.cs
├── Handlers/
│   ├── Commands/
│   │   ├── CreateContainerHandler.cs
│   │   ├── StartContainerHandler.cs
│   │   └── PullImageHandler.cs
│   └── Queries/
│       ├── GetContainersHandler.cs
│       └── GetImagesHandler.cs
├── DTOs/
│   ├── ContainerDto.cs
│   ├── ImageDto.cs
│   ├── NetworkDto.cs
│   └── VolumeDto.cs
├── Validators/
│   ├── CreateContainerValidator.cs
│   ├── PullImageValidator.cs
│   └── CreateNetworkValidator.cs
├── Mappers/
│   ├── ContainerMapper.cs
│   ├── ImageMapper.cs
│   └── NetworkMapper.cs
├── Services/
│   ├── ContainerApplicationService.cs
│   ├── ImageApplicationService.cs
│   └── TemplateApplicationService.cs
└── UseCases/
    ├── DeployApplicationUseCase.cs
    ├── BackupContainerUseCase.cs
    └── RestoreContainerUseCase.cs
```

### 3.3 Padrão CQRS Implementado

#### 3.3.1 Command Example

```csharp
using MediatR;
using AutoInstaller.Application.DTOs;

namespace AutoInstaller.Application.Commands.Containers;

public record CreateContainerCommand : IRequest<ContainerDto>
{
    public string Name { get; init; } = string.Empty;
    public string Image { get; init; } = string.Empty;
    public List<PortMappingDto> PortMappings { get; init; } = new();
    public List<VolumeMountDto> VolumeMounts { get; init; } = new();
    public List<EnvironmentVariableDto> Environment { get; init; } = new();
    public string? NetworkId { get; init; }
    public bool AutoStart { get; init; } = false;
}

public class CreateContainerHandler : IRequestHandler<CreateContainerCommand, ContainerDto>
{
    private readonly IContainerRepository _containerRepository;
    private readonly IContainerEngine _containerEngine;
    private readonly IMapper<Container, ContainerDto> _mapper;
    private readonly IValidator<CreateContainerCommand> _validator;
    private readonly ILogger<CreateContainerHandler> _logger;
    
    public CreateContainerHandler(
        IContainerRepository containerRepository,
        IContainerEngine containerEngine,
        IMapper<Container, ContainerDto> mapper,
        IValidator<CreateContainerCommand> validator,
        ILogger<CreateContainerHandler> logger)
    {
        _containerRepository = containerRepository;
        _containerEngine = containerEngine;
        _mapper = mapper;
        _validator = validator;
        _logger = logger;
    }
    
    public async Task<ContainerDto> Handle(CreateContainerCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating container {ContainerName} from image {Image}", request.Name, request.Image);
        
        // Validação
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            throw new ValidationException(validationResult.Errors);
        }
        
        // Criação da entidade de domínio
        var containerId = new ContainerId(Guid.NewGuid().ToString());
        var imageTag = new ImageTag(request.Image);
        var container = new Container(containerId, request.Name, imageTag);
        
        // Configuração de port mappings
        foreach (var portMapping in request.PortMappings)
        {
            container.AddPortMapping(new PortMapping(portMapping.HostPort, portMapping.ContainerPort, portMapping.Protocol));
        }
        
        // Configuração de volume mounts
        foreach (var volumeMount in request.VolumeMounts)
        {
            container.AddVolumeMount(new VolumeMount(volumeMount.HostPath, volumeMount.ContainerPath, volumeMount.ReadOnly));
        }
        
        // Configuração de variáveis de ambiente
        foreach (var envVar in request.Environment)
        {
            container.AddEnvironmentVariable(new EnvironmentVariable(envVar.Name, envVar.Value));
        }
        
        // Criação no container engine
        await _containerEngine.CreateContainerAsync(container, cancellationToken);
        
        // Persistência
        await _containerRepository.AddAsync(container, cancellationToken);
        
        // Auto start se solicitado
        if (request.AutoStart)
        {
            await _containerEngine.StartContainerAsync(container.Id, cancellationToken);
            container.Start();
            await _containerRepository.UpdateAsync(container, cancellationToken);
        }
        
        _logger.LogInformation("Container {ContainerName} created successfully with ID {ContainerId}", request.Name, container.Id);
        
        return _mapper.Map(container);
    }
}
```

#### 3.3.2 Query Example

```csharp
using MediatR;
using AutoInstaller.Application.DTOs;

namespace AutoInstaller.Application.Queries.Containers;

public record GetContainersQuery : IRequest<IEnumerable<ContainerDto>>
{
    public string? NameFilter { get; init; }
    public ContainerStatus? StatusFilter { get; init; }
    public string? ImageFilter { get; init; }
    public int Skip { get; init; } = 0;
    public int Take { get; init; } = 50;
}

public class GetContainersHandler : IRequestHandler<GetContainersQuery, IEnumerable<ContainerDto>>
{
    private readonly IContainerRepository _containerRepository;
    private readonly IMapper<Container, ContainerDto> _mapper;
    private readonly ILogger<GetContainersHandler> _logger;
    
    public GetContainersHandler(
        IContainerRepository containerRepository,
        IMapper<Container, ContainerDto> mapper,
        ILogger<GetContainersHandler> logger)
    {
        _containerRepository = containerRepository;
        _mapper = mapper;
        _logger = logger;
    }
    
    public async Task<IEnumerable<ContainerDto>> Handle(GetContainersQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Retrieving containers with filters: Name={NameFilter}, Status={StatusFilter}, Image={ImageFilter}", 
            request.NameFilter, request.StatusFilter, request.ImageFilter);
        
        var containers = await _containerRepository.GetAllAsync(
            nameFilter: request.NameFilter,
            statusFilter: request.StatusFilter,
            imageFilter: request.ImageFilter,
            skip: request.Skip,
            take: request.Take,
            cancellationToken: cancellationToken);
        
        var result = containers.Select(_mapper.Map).ToList();
        
        _logger.LogDebug("Retrieved {Count} containers", result.Count);
        
        return result;
    }
}
```

### 3.4 Validadores com FluentValidation

```csharp
using FluentValidation;
using AutoInstaller.Application.Commands.Containers;

namespace AutoInstaller.Application.Validators;

public class CreateContainerValidator : AbstractValidator<CreateContainerCommand>
{
    public CreateContainerValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Container name is required")
            .Length(1, 255).WithMessage("Container name must be between 1 and 255 characters")
            .Matches(@"^[a-zA-Z0-9][a-zA-Z0-9_.-]*$").WithMessage("Container name contains invalid characters");
            
        RuleFor(x => x.Image)
            .NotEmpty().WithMessage("Image is required")
            .Must(BeValidImageName).WithMessage("Invalid image name format");
            
        RuleForEach(x => x.PortMappings)
            .SetValidator(new PortMappingValidator());
            
        RuleForEach(x => x.VolumeMounts)
            .SetValidator(new VolumeMountValidator());
            
        RuleForEach(x => x.Environment)
            .SetValidator(new EnvironmentVariableValidator());
    }
    
    private bool BeValidImageName(string imageName)
    {
        // Validação de formato de nome de imagem Docker
        var pattern = @"^(?:[a-z0-9]+(?:[._-][a-z0-9]+)*\/)?[a-z0-9]+(?:[._-][a-z0-9]+)*(?::[a-zA-Z0-9][a-zA-Z0-9._-]*)?$";
        return Regex.IsMatch(imageName, pattern, RegexOptions.IgnoreCase);
    }
}
```

---

## 4. Camada de Infraestrutura (AutoInstaller.Infrastructure)

### 4.1 Responsabilidades
- Implementação de repositórios
- Clientes externos (Docker, Podman)
- Persistência de dados
- Logging
- Configurações
- Segurança
- Migrações

### 4.2 Estrutura de Pastas

```
AutoInstaller.Infrastructure/
├── Data/
│   ├── ApplicationDbContext.cs
│   ├── Configurations/
│   │   ├── ContainerConfiguration.cs
│   │   ├── ImageConfiguration.cs
│   │   └── NetworkConfiguration.cs
│   └── Seed/
│       └── DefaultDataSeeder.cs
├── Repositories/
│   ├── ContainerRepository.cs
│   ├── ImageRepository.cs
│   ├── NetworkRepository.cs
│   └── VolumeRepository.cs
├── External/
│   ├── Docker/
│   │   ├── DockerClientWrapper.cs
│   │   ├── DockerContainerService.cs
│   │   ├── DockerImageService.cs
│   │   └── DockerNetworkService.cs

├── Services/
│   ├── ContainerEngineFactory.cs
│   ├── TemplateService.cs
│   └── BackupService.cs
├── Security/
│   ├── CredentialManager.cs
│   ├── CertificateValidator.cs
│   └── SecretEncryption.cs
├── Logging/
│   ├── FileLogger.cs
│   ├── StructuredLogger.cs
│   └── LoggingExtensions.cs
├── Migrations/
│   └── [EF Core Migrations]
└── Extensions/
    ├── ServiceCollectionExtensions.cs
    └── ConfigurationExtensions.cs
```

### 4.3 Implementação de Repository

```csharp
using Microsoft.EntityFrameworkCore;
using AutoInstaller.Core.Entities;
using AutoInstaller.Core.Interfaces.Repositories;
using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Infrastructure.Data;
using AutoInstaller.Shared.Enums;

namespace AutoInstaller.Infrastructure.Repositories;

public class ContainerRepository : IContainerRepository
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<ContainerRepository> _logger;
    
    public ContainerRepository(ApplicationDbContext context, ILogger<ContainerRepository> logger)
    {
        _context = context;
        _logger = logger;
    }
    
    public async Task<Container?> GetByIdAsync(ContainerId id, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Retrieving container with ID {ContainerId}", id);
        
        return await _context.Containers
            .Include(c => c.PortMappings)
            .Include(c => c.VolumeMounts)
            .Include(c => c.Environment)
            .FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }
    
    public async Task<IEnumerable<Container>> GetAllAsync(
        string? nameFilter = null,
        ContainerStatus? statusFilter = null,
        string? imageFilter = null,
        int skip = 0,
        int take = 50,
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Retrieving containers with filters: Name={NameFilter}, Status={StatusFilter}, Image={ImageFilter}", 
            nameFilter, statusFilter, imageFilter);
        
        var query = _context.Containers
            .Include(c => c.PortMappings)
            .Include(c => c.VolumeMounts)
            .Include(c => c.Environment)
            .AsQueryable();
        
        if (!string.IsNullOrWhiteSpace(nameFilter))
        {
            query = query.Where(c => c.Name.Contains(nameFilter));
        }
        
        if (statusFilter.HasValue)
        {
            query = query.Where(c => c.Status == statusFilter.Value);
        }
        
        if (!string.IsNullOrWhiteSpace(imageFilter))
        {
            query = query.Where(c => c.Image.Value.Contains(imageFilter));
        }
        
        return await query
            .OrderBy(c => c.Name)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }
    
    public async Task AddAsync(Container container, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Adding container {ContainerName} with ID {ContainerId}", container.Name, container.Id);
        
        _context.Containers.Add(container);
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Container {ContainerName} added successfully", container.Name);
    }
    
    public async Task UpdateAsync(Container container, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Updating container {ContainerName} with ID {ContainerId}", container.Name, container.Id);
        
        _context.Containers.Update(container);
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogDebug("Container {ContainerName} updated successfully", container.Name);
    }
    
    public async Task RemoveAsync(ContainerId id, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Removing container with ID {ContainerId}", id);
        
        var container = await GetByIdAsync(id, cancellationToken);
        if (container != null)
        {
            _context.Containers.Remove(container);
            await _context.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Container {ContainerName} removed successfully", container.Name);
        }
    }
}
```

### 4.4 Factory Pattern para Container Engines

```csharp
using AutoInstaller.Core.Interfaces.Services;
using AutoInstaller.Infrastructure.External.Docker;
using AutoInstaller.Infrastructure.External.Podman;
using AutoInstaller.Shared.Enums;

namespace AutoInstaller.Infrastructure.Services;

public interface IContainerEngineFactory
{
    IContainerEngine CreateEngine(ContainerEngineType engineType);
    Task<IContainerEngine> CreateAvailableEngineAsync();
}

public class ContainerEngineFactory : IContainerEngineFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ContainerEngineFactory> _logger;
    
    public ContainerEngineFactory(IServiceProvider serviceProvider, ILogger<ContainerEngineFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }
    
    public IContainerEngine CreateEngine(ContainerEngineType engineType)
    {
        return engineType switch
        {
            ContainerEngineType.Docker => _serviceProvider.GetRequiredService<DockerContainerService>(),
            ContainerEngineType.Podman => _serviceProvider.GetRequiredService<PodmanContainerService>(),
            _ => throw new ArgumentException($"Unsupported container engine type: {engineType}")
        };
    }
    
    public async Task<IContainerEngine> CreateAvailableEngineAsync()
    {
        _logger.LogDebug("Detecting available container engine");
        
        // Tentar Docker primeiro
        try
        {
            var dockerEngine = CreateEngine(ContainerEngineType.Docker);
            if (await dockerEngine.IsAvailableAsync())
            {
                _logger.LogInformation("Docker engine detected and available");
                return dockerEngine;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Docker engine not available");
        }
        
        // Tentar Podman como fallback
        try
        {
            var podmanEngine = CreateEngine(ContainerEngineType.Podman);
            if (await podmanEngine.IsAvailableAsync())
            {
                _logger.LogInformation("Podman engine detected and available");
                return podmanEngine;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Podman engine not available");
        }
        
        throw new InvalidOperationException("No container engine available. Please install Docker or Podman.");
    }
}
```

---

## 5. Camada de Apresentação (AutoInstaller.UI)

### 5.1 Responsabilidades
- ViewModels (MVVM)
- Views (XAML)
- Converters
- Behaviors
- Controles customizados
- Serviços de UI

### 5.2 Estrutura de Pastas

```
AutoInstaller.UI/
├── ViewModels/
│   ├── Base/
│   │   ├── ViewModelBase.cs
│   │   ├── PageViewModelBase.cs
│   │   └── ModalViewModelBase.cs
│   ├── MainWindowViewModel.cs
│   ├── DashboardViewModel.cs
│   ├── ContainersViewModel.cs
│   ├── ImagesViewModel.cs
│   ├── NetworksViewModel.cs
│   ├── VolumesViewModel.cs
│   ├── ComposeViewModel.cs
│   └── SettingsViewModel.cs
├── Views/
│   ├── MainWindow.axaml
│   ├── DashboardView.axaml
│   ├── ContainersView.axaml
│   ├── ImagesView.axaml
│   ├── NetworksView.axaml
│   ├── VolumesView.axaml
│   ├── ComposeView.axaml
│   └── SettingsView.axaml
├── Controls/
│   ├── ContainerCard.axaml
│   ├── ImageCard.axaml
│   ├── StatCard.axaml
│   ├── ModalBase.axaml
│   └── LoadingSpinner.axaml
├── Modals/
│   ├── CreateContainerModal.axaml
│   ├── PullImageModal.axaml
│   ├── CreateNetworkModal.axaml
│   └── ConfirmationModal.axaml
├── Converters/
│   ├── StatusToColorConverter.cs
│   ├── BoolToVisibilityConverter.cs
│   ├── BytesToSizeConverter.cs
│   └── DateTimeToRelativeConverter.cs
├── Services/
│   ├── NavigationService.cs
│   ├── DialogService.cs
│   ├── NotificationService.cs
│   └── ThemeService.cs
├── Behaviors/
│   ├── DataGridSelectionBehavior.cs
│   └── TextBoxValidationBehavior.cs
└── Extensions/
    ├── ViewModelExtensions.cs
    └── ControlExtensions.cs
```

### 5.3 Base ViewModel

```csharp
using ReactiveUI;
using System.Reactive.Disposables;
using MediatR;

namespace AutoInstaller.UI.ViewModels.Base;

public abstract class ViewModelBase : ReactiveObject, IActivatableViewModel, IDisposable
{
    protected readonly IMediator Mediator;
    protected readonly ILogger Logger;
    protected readonly CompositeDisposable Disposables = new();
    
    public ViewModelActivator Activator { get; } = new();
    
    protected ViewModelBase(IMediator mediator, ILogger logger)
    {
        Mediator = mediator;
        Logger = logger;
        
        this.WhenActivated(disposables =>
        {
            OnActivated();
            Disposable.Create(OnDeactivated).DisposeWith(disposables);
        });
    }
    
    protected virtual void OnActivated() { }
    protected virtual void OnDeactivated() { }
    
    public virtual void Dispose()
    {
        Disposables?.Dispose();
        GC.SuppressFinalize(this);
    }
}

public abstract class PageViewModelBase : ViewModelBase
{
    private string _title = string.Empty;
    private bool _isLoading;
    private string? _errorMessage;
    
    public string Title
    {
        get => _title;
        set => this.RaiseAndSetIfChanged(ref _title, value);
    }
    
    public bool IsLoading
    {
        get => _isLoading;
        set => this.RaiseAndSetIfChanged(ref _isLoading, value);
    }
    
    public string? ErrorMessage
    {
        get => _errorMessage;
        set => this.RaiseAndSetIfChanged(ref _errorMessage, value);
    }
    
    protected PageViewModelBase(IMediator mediator, ILogger logger) : base(mediator, logger)
    {
    }
    
    protected async Task ExecuteWithLoadingAsync(Func<Task> action, string? loadingMessage = null)
    {
        try
        {
            IsLoading = true;
            ErrorMessage = null;
            
            await action();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error executing action: {ErrorMessage}", ex.Message);
            ErrorMessage = ex.Message;
        }
        finally
        {
            IsLoading = false;
        }
    }
    
    protected async Task<T?> ExecuteWithLoadingAsync<T>(Func<Task<T>> action, string? loadingMessage = null)
    {
        try
        {
            IsLoading = true;
            ErrorMessage = null;
            
            return await action();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error executing action: {ErrorMessage}", ex.Message);
            ErrorMessage = ex.Message;
            return default;
        }
        finally
        {
            IsLoading = false;
        }
    }
}
```

---

## 6. Camada Compartilhada (AutoInstaller.Shared)

### 6.1 Responsabilidades
- Enums compartilhados
- Constantes
- Modelos compartilhados
- Utilitários
- Extensions methods

### 6.2 Estrutura de Pastas

```
AutoInstaller.Shared/
├── Enums/
│   ├── ContainerStatus.cs
│   ├── ContainerEngineType.cs
│   ├── ImageStatus.cs
│   ├── NetworkType.cs
│   └── VolumeType.cs
├── Constants/
│   ├── ApplicationConstants.cs
│   ├── DockerConstants.cs
│   └── PodmanConstants.cs
├── Models/
│   ├── Result.cs
│   ├── PagedResult.cs
│   └── OperationResult.cs
├── Extensions/
│   ├── StringExtensions.cs
│   ├── DateTimeExtensions.cs
│   └── CollectionExtensions.cs
└── Utilities/
    ├── PathHelper.cs
    ├── NetworkHelper.cs
    └── ValidationHelper.cs
```

### 6.3 Result Pattern

```csharp
namespace AutoInstaller.Shared.Models;

public class Result
{
    public bool IsSuccess { get; }
    public bool IsFailure => !IsSuccess;
    public string? ErrorMessage { get; }
    public Exception? Exception { get; }
    
    protected Result(bool isSuccess, string? errorMessage = null, Exception? exception = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
        Exception = exception;
    }
    
    public static Result Success() => new(true);
    public static Result Failure(string errorMessage) => new(false, errorMessage);
    public static Result Failure(Exception exception) => new(false, exception.Message, exception);
    
    public static Result<T> Success<T>(T value) => new(value, true);
    public static Result<T> Failure<T>(string errorMessage) => new(default, false, errorMessage);
    public static Result<T> Failure<T>(Exception exception) => new(default, false, exception.Message, exception);
}

public class Result<T> : Result
{
    public T? Value { get; }
    
    internal Result(T? value, bool isSuccess, string? errorMessage = null, Exception? exception = null)
        : base(isSuccess, errorMessage, exception)
    {
        Value = value;
    }
    
    public static implicit operator Result<T>(T value) => Success(value);
}
```

---

## 7. Padrões de Projeto Aplicados

### 7.1 Repository Pattern
- Abstração do acesso a dados
- Facilita testes unitários
- Centraliza lógica de consulta

### 7.2 Factory Pattern
- Criação de container engines
- Instanciação de serviços
- Configuração baseada em contexto

### 7.3 Command Pattern (CQRS)
- Separação de comandos e consultas
- Melhor testabilidade
- Escalabilidade

### 7.4 Observer Pattern (ReactiveUI)
- Binding reativo
- Notificação de mudanças
- Programação funcional reativa

### 7.5 Dependency Injection
- Inversão de controle
- Facilita testes
- Baixo acoplamento

### 7.6 Strategy Pattern
- Diferentes engines de container
- Algoritmos de backup
- Estratégias de deployment

### 7.7 Template Method Pattern
- Base classes para ViewModels
- Fluxos de operação padronizados
- Hooks para customização

---

## 8. Injeção de Dependências

### 8.1 Configuração no Host

```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using AutoInstaller.Infrastructure.Extensions;
using AutoInstaller.Application.Extensions;
using AutoInstaller.UI.Extensions;

namespace AutoInstaller.Host;

public class Program
{
    public static void Main(string[] args)
    {
        var host = CreateHostBuilder(args).Build();
        
        // Inicializar aplicação Avalonia
        var app = host.Services.GetRequiredService<App>();
        app.Run(args);
    }
    
    private static IHostBuilder CreateHostBuilder(string[] args) =>
        Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder(args)
            .ConfigureServices((context, services) =>
            {
                // Configurar camadas
                services.AddInfrastructure(context.Configuration);
                services.AddApplication();
                services.AddUI();
                
                // Configurar logging
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.AddFile("logs/autoinstaller.log");
                });
            });
}
```

### 8.2 Extensões de Configuração

```csharp
// AutoInstaller.Infrastructure/Extensions/ServiceCollectionExtensions.cs
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using AutoInstaller.Core.Interfaces.Repositories;
using AutoInstaller.Infrastructure.Repositories;
using AutoInstaller.Infrastructure.Data;
using AutoInstaller.Infrastructure.Services;

namespace AutoInstaller.Infrastructure.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseSqlite(configuration.GetConnectionString("DefaultConnection")));
        
        // Repositories
        services.AddScoped<IContainerRepository, ContainerRepository>();
        services.AddScoped<IImageRepository, ImageRepository>();
        services.AddScoped<INetworkRepository, NetworkRepository>();
        services.AddScoped<IVolumeRepository, VolumeRepository>();
        
        // Services
        services.AddScoped<IContainerEngineFactory, ContainerEngineFactory>();
        services.AddScoped<DockerContainerService>();

        // External clients
        services.AddSingleton<DockerClientWrapper>();
        
        return services;
    }
}
```

---

## 9. Testes e Qualidade

### 9.1 Estrutura de Testes

```
tests/
├── AutoInstaller.Core.Tests/
│   ├── Entities/
│   ├── Services/
│   └── ValueObjects/
├── AutoInstaller.Application.Tests/
│   ├── Commands/
│   ├── Queries/
│   ├── Handlers/
│   └── Validators/
├── AutoInstaller.Infrastructure.Tests/
│   ├── Repositories/
│   ├── Services/
│   └── External/
└── AutoInstaller.UI.Tests/
    ├── ViewModels/
    ├── Converters/
    └── Services/
```

### 9.2 Exemplo de Teste Unitário

```csharp
using Xunit;
using FluentAssertions;
using AutoInstaller.Core.Entities;
using AutoInstaller.Core.ValueObjects;
using AutoInstaller.Shared.Enums;

namespace AutoInstaller.Core.Tests.Entities;

public class ContainerTests
{
    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateContainer()
    {
        // Arrange
        var id = new ContainerId("test-container-id");
        var name = "test-container";
        var image = new ImageTag("nginx:latest");
        
        // Act
        var container = new Container(id, name, image);
        
        // Assert
        container.Id.Should().Be(id);
        container.Name.Should().Be(name);
        container.Image.Should().Be(image);
        container.Status.Should().Be(ContainerStatus.Created);
        container.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }
    
    [Fact]
    public void Start_WhenContainerIsCreated_ShouldChangeStatusToRunning()
    {
        // Arrange
        var container = CreateTestContainer();
        
        // Act
        container.Start();
        
        // Assert
        container.Status.Should().Be(ContainerStatus.Running);
        container.StartedAt.Should().NotBeNull();
        container.StartedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }
    
    [Fact]
    public void Start_WhenContainerIsAlreadyRunning_ShouldThrowException()
    {
        // Arrange
        var container = CreateTestContainer();
        container.Start();
        
        // Act & Assert
        container.Invoking(c => c.Start())
            .Should().Throw<InvalidOperationException>()
            .WithMessage("Container is already running");
    }
    
    private static Container CreateTestContainer()
    {
        return new Container(
            new ContainerId("test-container-id"),
            "test-container",
            new ImageTag("nginx:latest"));
    }
}
```

---

## 10. Considerações de Performance

### 10.1 Otimizações de Database
- Índices apropriados
- Consultas otimizadas
- Lazy loading configurado
- Connection pooling

### 10.2 Otimizações de UI
- Virtualização de listas
- Binding compilado
- Throttling de eventos
- Caching de ViewModels

### 10.3 Otimizações de Memória
- Disposable pattern
- Weak references
- Object pooling
- Garbage collection otimizado

---

*Documento gerado em: Janeiro 2025*
*Versão: 1.0*
*Autor: Arquiteto de Software - Auto-Instalador*