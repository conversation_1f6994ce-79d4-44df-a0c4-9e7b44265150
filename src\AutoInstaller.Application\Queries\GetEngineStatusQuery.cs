using AutoInstaller.Core.Enums;
using MediatR;

namespace AutoInstaller.Application.Queries;

/// <summary>
/// Query para obter status das engines embarcadas
/// </summary>
public record GetEngineStatusQuery : IRequest<GetEngineStatusResult>
{
    /// <summary>
    /// Nome da engine específica (null = todas)
    /// </summary>
    public string? EngineName { get; init; }

    /// <summary>
    /// Tipo da engine específica (null = todos)
    /// </summary>
    public EngineType? EngineType { get; init; }

    /// <summary>
    /// Incluir informações detalhadas
    /// </summary>
    public bool IncludeDetails { get; init; } = false;

    /// <summary>
    /// Incluir health check
    /// </summary>
    public bool IncludeHealthCheck { get; init; } = true;
}

/// <summary>
/// Resultado da query de status das engines
/// </summary>
public record GetEngineStatusResult
{
    /// <summary>
    /// Lista de engines e seus status
    /// </summary>
    public List<EngineStatusDto> Engines { get; init; } = new();

    /// <summary>
    /// Timestamp da consulta
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// Cria resultado com engines
    /// </summary>
    public static GetEngineStatusResult Create(List<EngineStatusDto> engines)
    {
        return new GetEngineStatusResult { Engines = engines };
    }
}

/// <summary>
/// DTO para status de engine
/// </summary>
public record EngineStatusDto
{
    /// <summary>
    /// ID da engine
    /// </summary>
    public string Id { get; init; } = string.Empty;

    /// <summary>
    /// Nome da engine
    /// </summary>
    public string Name { get; init; } = string.Empty;

    /// <summary>
    /// Tipo da engine
    /// </summary>
    public EngineType Type { get; init; }

    /// <summary>
    /// Status atual
    /// </summary>
    public EngineStatus Status { get; init; }

    /// <summary>
    /// Versão da engine
    /// </summary>
    public string Version { get; init; } = string.Empty;

    /// <summary>
    /// Sistema operacional
    /// </summary>
    public OperatingSystem OperatingSystem { get; init; }

    /// <summary>
    /// Arquitetura
    /// </summary>
    public string Architecture { get; init; } = string.Empty;

    /// <summary>
    /// Data da última inicialização
    /// </summary>
    public DateTime? LastStarted { get; init; }

    /// <summary>
    /// Data da última parada
    /// </summary>
    public DateTime? LastStopped { get; init; }

    /// <summary>
    /// Process ID (se em execução)
    /// </summary>
    public int? ProcessId { get; init; }

    /// <summary>
    /// Endpoint de comunicação
    /// </summary>
    public string? CommunicationEndpoint { get; init; }

    /// <summary>
    /// Status de saúde
    /// </summary>
    public HealthStatus HealthStatus { get; init; }

    /// <summary>
    /// Número de containers gerenciados
    /// </summary>
    public int ContainerCount { get; init; }

    /// <summary>
    /// Uso de CPU (%)
    /// </summary>
    public double? CpuUsagePercent { get; init; }

    /// <summary>
    /// Uso de memória em bytes
    /// </summary>
    public long? MemoryUsageBytes { get; init; }

    /// <summary>
    /// Tempo de atividade em segundos
    /// </summary>
    public double? UptimeSeconds { get; init; }

    /// <summary>
    /// Mensagem de erro (se houver)
    /// </summary>
    public string? ErrorMessage { get; init; }
}

/// <summary>
/// Query para obter lista de containers
/// </summary>
public record GetContainersQuery : IRequest<GetContainersResult>
{
    /// <summary>
    /// ID da engine específica (null = todas)
    /// </summary>
    public string? EngineId { get; init; }

    /// <summary>
    /// Status específico (null = todos)
    /// </summary>
    public ContainerStatus? Status { get; init; }

    /// <summary>
    /// Incluir containers parados
    /// </summary>
    public bool IncludeStopped { get; init; } = true;

    /// <summary>
    /// Incluir informações detalhadas
    /// </summary>
    public bool IncludeDetails { get; init; } = false;

    /// <summary>
    /// Limite de resultados
    /// </summary>
    public int? Limit { get; init; }

    /// <summary>
    /// Offset para paginação
    /// </summary>
    public int Offset { get; init; } = 0;
}

/// <summary>
/// Resultado da query de containers
/// </summary>
public record GetContainersResult
{
    /// <summary>
    /// Lista de containers
    /// </summary>
    public List<ContainerDto> Containers { get; init; } = new();

    /// <summary>
    /// Total de containers (para paginação)
    /// </summary>
    public int TotalCount { get; init; }

    /// <summary>
    /// Timestamp da consulta
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// Cria resultado com containers
    /// </summary>
    public static GetContainersResult Create(List<ContainerDto> containers, int totalCount)
    {
        return new GetContainersResult 
        { 
            Containers = containers, 
            TotalCount = totalCount 
        };
    }
}

/// <summary>
/// DTO para container
/// </summary>
public record ContainerDto
{
    /// <summary>
    /// ID do container
    /// </summary>
    public string Id { get; init; } = string.Empty;

    /// <summary>
    /// Nome do container
    /// </summary>
    public string Name { get; init; } = string.Empty;

    /// <summary>
    /// ID do container na engine
    /// </summary>
    public string? EngineContainerId { get; init; }

    /// <summary>
    /// Nome da imagem
    /// </summary>
    public string ImageName { get; init; } = string.Empty;

    /// <summary>
    /// Tag da imagem
    /// </summary>
    public string ImageTag { get; init; } = string.Empty;

    /// <summary>
    /// Status atual
    /// </summary>
    public ContainerStatus Status { get; init; }

    /// <summary>
    /// ID da engine embarcada
    /// </summary>
    public string EmbeddedEngineId { get; init; } = string.Empty;

    /// <summary>
    /// Tipo da engine
    /// </summary>
    public EngineType EngineType { get; init; }

    /// <summary>
    /// Data de criação
    /// </summary>
    public DateTime CreatedAt { get; init; }

    /// <summary>
    /// Data de início
    /// </summary>
    public DateTime? StartedAt { get; init; }

    /// <summary>
    /// Data de parada
    /// </summary>
    public DateTime? StoppedAt { get; init; }

    /// <summary>
    /// Código de saída
    /// </summary>
    public int? ExitCode { get; init; }

    /// <summary>
    /// Mapeamentos de porta
    /// </summary>
    public List<string> Ports { get; init; } = new();

    /// <summary>
    /// Montagens de volume
    /// </summary>
    public List<string> Volumes { get; init; } = new();

    /// <summary>
    /// Variáveis de ambiente (mascaradas se sensíveis)
    /// </summary>
    public Dictionary<string, string> Environment { get; init; } = new();

    /// <summary>
    /// Uso de CPU (%)
    /// </summary>
    public double? CpuUsagePercent { get; init; }

    /// <summary>
    /// Uso de memória em bytes
    /// </summary>
    public long? MemoryUsageBytes { get; init; }

    /// <summary>
    /// Limite de memória em bytes
    /// </summary>
    public long? MemoryLimitBytes { get; init; }

    /// <summary>
    /// I/O de rede em bytes
    /// </summary>
    public long? NetworkIOBytes { get; init; }

    /// <summary>
    /// I/O de disco em bytes
    /// </summary>
    public long? DiskIOBytes { get; init; }

    /// <summary>
    /// Mensagem de erro (se houver)
    /// </summary>
    public string? ErrorMessage { get; init; }
}

/// <summary>
/// Query para obter logs de engine ou container
/// </summary>
public record GetLogsQuery : IRequest<GetLogsResult>
{
    /// <summary>
    /// ID da engine (para logs da engine)
    /// </summary>
    public string? EngineId { get; init; }

    /// <summary>
    /// ID do container (para logs do container)
    /// </summary>
    public string? ContainerId { get; init; }

    /// <summary>
    /// Data de início dos logs
    /// </summary>
    public DateTime? Since { get; init; }

    /// <summary>
    /// Data de fim dos logs
    /// </summary>
    public DateTime? Until { get; init; }

    /// <summary>
    /// Número de linhas (tail)
    /// </summary>
    public int? Lines { get; init; }

    /// <summary>
    /// Incluir timestamps
    /// </summary>
    public bool IncludeTimestamps { get; init; } = true;

    /// <summary>
    /// Seguir logs (stream)
    /// </summary>
    public bool Follow { get; init; } = false;

    /// <summary>
    /// Filtro de nível de log
    /// </summary>
    public string? LogLevel { get; init; }
}

/// <summary>
/// Resultado da query de logs
/// </summary>
public record GetLogsResult
{
    /// <summary>
    /// Linhas de log
    /// </summary>
    public List<LogEntryDto> Logs { get; init; } = new();

    /// <summary>
    /// Indica se há mais logs disponíveis
    /// </summary>
    public bool HasMore { get; init; }

    /// <summary>
    /// Token para próxima página
    /// </summary>
    public string? NextPageToken { get; init; }

    /// <summary>
    /// Cria resultado com logs
    /// </summary>
    public static GetLogsResult Create(List<LogEntryDto> logs, bool hasMore = false, string? nextPageToken = null)
    {
        return new GetLogsResult 
        { 
            Logs = logs, 
            HasMore = hasMore, 
            NextPageToken = nextPageToken 
        };
    }
}

/// <summary>
/// DTO para entrada de log
/// </summary>
public record LogEntryDto
{
    /// <summary>
    /// Timestamp do log
    /// </summary>
    public DateTime Timestamp { get; init; }

    /// <summary>
    /// Nível do log
    /// </summary>
    public string Level { get; init; } = string.Empty;

    /// <summary>
    /// Fonte do log (engine, container, system)
    /// </summary>
    public string Source { get; init; } = string.Empty;

    /// <summary>
    /// Mensagem do log
    /// </summary>
    public string Message { get; init; } = string.Empty;

    /// <summary>
    /// Propriedades adicionais
    /// </summary>
    public Dictionary<string, object> Properties { get; init; } = new();
}
