namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value Object para mapeamento de portas de containers
/// </summary>
public class PortMapping : ValueObject
{
    /// <summary>
    /// Porta no host
    /// </summary>
    public int HostPort { get; }

    /// <summary>
    /// Porta no container
    /// </summary>
    public int ContainerPort { get; }

    /// <summary>
    /// Protocolo (TCP, UDP)
    /// </summary>
    public string Protocol { get; }

    /// <summary>
    /// IP do host para bind (null = todos os IPs)
    /// </summary>
    public string? HostIp { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    public PortMapping(int hostPort, int containerPort, string protocol = "tcp", string? hostIp = null)
    {
        if (hostPort <= 0 || hostPort > 65535)
            throw new ArgumentException("Porta do host deve estar entre 1 e 65535", nameof(hostPort));

        if (containerPort <= 0 || containerPort > 65535)
            throw new ArgumentException("Porta do container deve estar entre 1 e 65535", nameof(containerPort));

        if (string.IsNullOrWhiteSpace(protocol))
            throw new ArgumentException("Protocolo é obrigatório", nameof(protocol));

        HostPort = hostPort;
        ContainerPort = containerPort;
        Protocol = protocol.ToLowerInvariant();
        HostIp = hostIp;
    }

    /// <summary>
    /// Cria mapeamento TCP
    /// </summary>
    public static PortMapping Tcp(int hostPort, int containerPort, string? hostIp = null)
    {
        return new PortMapping(hostPort, containerPort, "tcp", hostIp);
    }

    /// <summary>
    /// Cria mapeamento UDP
    /// </summary>
    public static PortMapping Udp(int hostPort, int containerPort, string? hostIp = null)
    {
        return new PortMapping(hostPort, containerPort, "udp", hostIp);
    }

    /// <summary>
    /// Cria mapeamento com mesma porta
    /// </summary>
    public static PortMapping Same(int port, string protocol = "tcp", string? hostIp = null)
    {
        return new PortMapping(port, port, protocol, hostIp);
    }

    /// <summary>
    /// Converte para string no formato Docker/Podman
    /// </summary>
    public string ToDockerFormat()
    {
        var hostPart = string.IsNullOrWhiteSpace(HostIp) ? HostPort.ToString() : $"{HostIp}:{HostPort}";
        return $"{hostPart}:{ContainerPort}/{Protocol}";
    }

    /// <summary>
    /// Verifica se é uma porta bem conhecida (1-1023)
    /// </summary>
    public bool IsWellKnownPort()
    {
        return ContainerPort <= 1023;
    }

    /// <summary>
    /// Verifica se é uma porta registrada (1024-49151)
    /// </summary>
    public bool IsRegisteredPort()
    {
        return ContainerPort >= 1024 && ContainerPort <= 49151;
    }

    /// <summary>
    /// Verifica se é uma porta dinâmica/privada (49152-65535)
    /// </summary>
    public bool IsDynamicPort()
    {
        return ContainerPort >= 49152;
    }

    /// <summary>
    /// Implementação de igualdade para Value Object
    /// </summary>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return HostPort;
        yield return ContainerPort;
        yield return Protocol;
        yield return HostIp ?? string.Empty;
    }

    /// <summary>
    /// Override do ToString
    /// </summary>
    public override string ToString()
    {
        return ToDockerFormat();
    }
}

/// <summary>
/// Value Object para montagem de volumes
/// </summary>
public class VolumeMount : ValueObject
{
    /// <summary>
    /// Caminho no host
    /// </summary>
    public string HostPath { get; }

    /// <summary>
    /// Caminho no container
    /// </summary>
    public string ContainerPath { get; }

    /// <summary>
    /// Tipo de montagem (bind, volume, tmpfs)
    /// </summary>
    public string Type { get; }

    /// <summary>
    /// Modo de acesso (ro, rw)
    /// </summary>
    public string Mode { get; }

    /// <summary>
    /// Opções adicionais
    /// </summary>
    public string[] Options { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    public VolumeMount(
        string hostPath, 
        string containerPath, 
        string type = "bind", 
        string mode = "rw", 
        string[]? options = null)
    {
        if (string.IsNullOrWhiteSpace(hostPath))
            throw new ArgumentException("Caminho do host é obrigatório", nameof(hostPath));

        if (string.IsNullOrWhiteSpace(containerPath))
            throw new ArgumentException("Caminho do container é obrigatório", nameof(containerPath));

        if (string.IsNullOrWhiteSpace(type))
            throw new ArgumentException("Tipo de montagem é obrigatório", nameof(type));

        if (string.IsNullOrWhiteSpace(mode))
            throw new ArgumentException("Modo de acesso é obrigatório", nameof(mode));

        HostPath = hostPath;
        ContainerPath = containerPath;
        Type = type.ToLowerInvariant();
        Mode = mode.ToLowerInvariant();
        Options = options ?? Array.Empty<string>();
    }

    /// <summary>
    /// Cria montagem bind read-write
    /// </summary>
    public static VolumeMount Bind(string hostPath, string containerPath, bool readOnly = false)
    {
        return new VolumeMount(hostPath, containerPath, "bind", readOnly ? "ro" : "rw");
    }

    /// <summary>
    /// Cria montagem de volume nomeado
    /// </summary>
    public static VolumeMount Volume(string volumeName, string containerPath, bool readOnly = false)
    {
        return new VolumeMount(volumeName, containerPath, "volume", readOnly ? "ro" : "rw");
    }

    /// <summary>
    /// Cria montagem tmpfs
    /// </summary>
    public static VolumeMount Tmpfs(string containerPath, string[]? options = null)
    {
        return new VolumeMount("tmpfs", containerPath, "tmpfs", "rw", options);
    }

    /// <summary>
    /// Verifica se é somente leitura
    /// </summary>
    public bool IsReadOnly => Mode.Contains("ro");

    /// <summary>
    /// Verifica se é montagem bind
    /// </summary>
    public bool IsBind => Type == "bind";

    /// <summary>
    /// Verifica se é volume nomeado
    /// </summary>
    public bool IsVolume => Type == "volume";

    /// <summary>
    /// Verifica se é tmpfs
    /// </summary>
    public bool IsTmpfs => Type == "tmpfs";

    /// <summary>
    /// Converte para string no formato Docker/Podman
    /// </summary>
    public string ToDockerFormat()
    {
        var result = $"{HostPath}:{ContainerPath}";
        
        if (Mode != "rw")
        {
            result += $":{Mode}";
        }

        if (Options.Length > 0)
        {
            result += $":{string.Join(",", Options)}";
        }

        return result;
    }

    /// <summary>
    /// Implementação de igualdade para Value Object
    /// </summary>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return HostPath;
        yield return ContainerPath;
        yield return Type;
        yield return Mode;
        foreach (var option in Options.OrderBy(o => o))
            yield return option;
    }

    /// <summary>
    /// Override do ToString
    /// </summary>
    public override string ToString()
    {
        return ToDockerFormat();
    }
}

/// <summary>
/// Value Object para variáveis de ambiente
/// </summary>
public class EnvironmentVariable : ValueObject
{
    /// <summary>
    /// Nome da variável
    /// </summary>
    public string Name { get; }

    /// <summary>
    /// Valor da variável
    /// </summary>
    public string Value { get; }

    /// <summary>
    /// Indica se é uma variável sensível (senha, token, etc.)
    /// </summary>
    public bool IsSensitive { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    public EnvironmentVariable(string name, string value, bool isSensitive = false)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Nome da variável é obrigatório", nameof(name));

        Name = name;
        Value = value ?? string.Empty;
        IsSensitive = isSensitive;
    }

    /// <summary>
    /// Cria variável de ambiente comum
    /// </summary>
    public static EnvironmentVariable Create(string name, string value)
    {
        return new EnvironmentVariable(name, value, false);
    }

    /// <summary>
    /// Cria variável de ambiente sensível
    /// </summary>
    public static EnvironmentVariable CreateSensitive(string name, string value)
    {
        return new EnvironmentVariable(name, value, true);
    }

    /// <summary>
    /// Converte para string no formato Docker/Podman
    /// </summary>
    public string ToDockerFormat()
    {
        return $"{Name}={Value}";
    }

    /// <summary>
    /// Obtém valor mascarado para logs
    /// </summary>
    public string GetMaskedValue()
    {
        if (!IsSensitive || string.IsNullOrEmpty(Value))
            return Value;

        if (Value.Length <= 4)
            return "****";

        return Value.Substring(0, 2) + new string('*', Value.Length - 4) + Value.Substring(Value.Length - 2);
    }

    /// <summary>
    /// Implementação de igualdade para Value Object
    /// </summary>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Name;
        yield return Value;
        yield return IsSensitive;
    }

    /// <summary>
    /// Override do ToString
    /// </summary>
    public override string ToString()
    {
        return IsSensitive ? $"{Name}={GetMaskedValue()}" : ToDockerFormat();
    }
}
