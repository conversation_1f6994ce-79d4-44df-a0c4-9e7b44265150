using AutoInstaller.Core.Enums;
using AutoInstaller.Core.Events;
using AutoInstaller.Core.Exceptions;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Core.Entities;

/// <summary>
/// Entidade de domínio para engines embarcadas (<PERSON><PERSON>/<PERSON>dman)
/// Representa uma engine de container que pode ser extraída e executada localmente
/// </summary>
public class EmbeddedEngine : Entity
{
    private readonly List<DomainEvent> _domainEvents = new();

    /// <summary>
    /// Nome da engine embarcada
    /// </summary>
    public string Name { get; private set; }

    /// <summary>
    /// Tipo da engine (Docker ou Podman)
    /// </summary>
    public EngineType Type { get; private set; }

    /// <summary>
    /// Status atual da engine
    /// </summary>
    public EngineStatus Status { get; private set; }

    /// <summary>
    /// Caminho para os binários da engine
    /// </summary>
    public string BinaryPath { get; private set; }

    /// <summary>
    /// Versão da engine embarcada
    /// </summary>
    public string Version { get; private set; }

    /// <summary>
    /// Sistema operacional suportado
    /// </summary>
    public OperatingSystem OperatingSystem { get; private set; }

    /// <summary>
    /// Arquitetura suportada (x64, arm64)
    /// </summary>
    public string Architecture { get; private set; }

    /// <summary>
    /// Data da última inicialização
    /// </summary>
    public DateTime? LastStarted { get; private set; }

    /// <summary>
    /// Data da última parada
    /// </summary>
    public DateTime? LastStopped { get; private set; }

    /// <summary>
    /// Configurações específicas da engine
    /// </summary>
    public EngineConfiguration Configuration { get; private set; }

    /// <summary>
    /// Indica se a engine foi extraída e está pronta para uso
    /// </summary>
    public bool IsExtracted { get; private set; }

    /// <summary>
    /// Indica se a engine está configurada corretamente
    /// </summary>
    public bool IsConfigured { get; private set; }

    /// <summary>
    /// Processo ID quando a engine está em execução
    /// </summary>
    public int? ProcessId { get; private set; }

    /// <summary>
    /// Porta ou socket usado para comunicação
    /// </summary>
    public string? CommunicationEndpoint { get; private set; }

    /// <summary>
    /// Construtor privado para Entity Framework
    /// </summary>
    private EmbeddedEngine() 
    {
        Name = string.Empty;
        BinaryPath = string.Empty;
        Version = string.Empty;
        Architecture = string.Empty;
        Configuration = new EngineConfiguration();
    }

    /// <summary>
    /// Construtor para criar nova engine embarcada
    /// </summary>
    private EmbeddedEngine(
        string name, 
        EngineType type, 
        string binaryPath, 
        string version,
        OperatingSystem operatingSystem,
        string architecture) : this()
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new DomainException("Nome da engine é obrigatório");

        if (string.IsNullOrWhiteSpace(binaryPath))
            throw new DomainException("Caminho dos binários é obrigatório");

        if (string.IsNullOrWhiteSpace(version))
            throw new DomainException("Versão da engine é obrigatória");

        Name = name;
        Type = type;
        BinaryPath = binaryPath;
        Version = version;
        OperatingSystem = operatingSystem;
        Architecture = architecture;
        Status = EngineStatus.NotExtracted;
        Configuration = new EngineConfiguration();
        IsExtracted = false;
        IsConfigured = false;

        AddDomainEvent(new EmbeddedEngineCreatedEvent(Id, Name, Type, Version));
    }

    /// <summary>
    /// Factory method para criar nova engine embarcada
    /// </summary>
    public static EmbeddedEngine Create(
        string name,
        EngineType type,
        string binaryPath,
        string version,
        OperatingSystem operatingSystem,
        string architecture)
    {
        return new EmbeddedEngine(name, type, binaryPath, version, operatingSystem, architecture);
    }

    /// <summary>
    /// Marca a engine como extraída
    /// </summary>
    public void MarkAsExtracted(string extractedPath)
    {
        if (Status != EngineStatus.NotExtracted)
            throw new DomainException($"Engine {Name} já foi extraída");

        if (string.IsNullOrWhiteSpace(extractedPath))
            throw new DomainException("Caminho de extração é obrigatório");

        BinaryPath = extractedPath;
        Status = EngineStatus.Extracted;
        IsExtracted = true;

        AddDomainEvent(new EmbeddedEngineExtractedEvent(Id, Name, Type, extractedPath));
    }

    /// <summary>
    /// Marca a engine como configurada
    /// </summary>
    public void MarkAsConfigured(EngineConfiguration configuration, string communicationEndpoint)
    {
        if (!IsExtracted)
            throw new DomainException($"Engine {Name} deve ser extraída antes de ser configurada");

        if (configuration == null)
            throw new DomainException("Configuração é obrigatória");

        Configuration = configuration;
        CommunicationEndpoint = communicationEndpoint;
        Status = EngineStatus.Configured;
        IsConfigured = true;

        AddDomainEvent(new EmbeddedEngineConfiguredEvent(Id, Name, Type, communicationEndpoint));
    }

    /// <summary>
    /// Inicia a engine embarcada
    /// </summary>
    public void Start(int processId)
    {
        if (!IsConfigured)
            throw new DomainException($"Engine {Name} deve ser configurada antes de ser iniciada");

        if (Status == EngineStatus.Running)
            throw new DomainException($"Engine {Name} já está em execução");

        if (processId <= 0)
            throw new DomainException("Process ID deve ser válido");

        Status = EngineStatus.Starting;
        ProcessId = processId;
        LastStarted = DateTime.UtcNow;

        AddDomainEvent(new EmbeddedEngineStartingEvent(Id, Name, Type, processId));
    }

    /// <summary>
    /// Marca a engine como em execução
    /// </summary>
    public void MarkAsRunning()
    {
        if (Status != EngineStatus.Starting)
            throw new DomainException($"Engine {Name} deve estar iniciando para ser marcada como em execução");

        Status = EngineStatus.Running;
        AddDomainEvent(new EmbeddedEngineRunningEvent(Id, Name, Type, ProcessId!.Value));
    }

    /// <summary>
    /// Para a engine embarcada
    /// </summary>
    public void Stop()
    {
        if (Status != EngineStatus.Running)
            throw new DomainException($"Engine {Name} não está em execução");

        Status = EngineStatus.Stopping;
        LastStopped = DateTime.UtcNow;

        AddDomainEvent(new EmbeddedEngineStoppingEvent(Id, Name, Type, ProcessId!.Value));
    }

    /// <summary>
    /// Marca a engine como parada
    /// </summary>
    public void MarkAsStopped()
    {
        if (Status != EngineStatus.Stopping)
            throw new DomainException($"Engine {Name} deve estar parando para ser marcada como parada");

        Status = EngineStatus.Stopped;
        ProcessId = null;
        CommunicationEndpoint = null;

        AddDomainEvent(new EmbeddedEngineStoppedEvent(Id, Name, Type));
    }

    /// <summary>
    /// Marca a engine com erro
    /// </summary>
    public void MarkAsError(string errorMessage)
    {
        if (string.IsNullOrWhiteSpace(errorMessage))
            throw new DomainException("Mensagem de erro é obrigatória");

        Status = EngineStatus.Error;
        ProcessId = null;
        CommunicationEndpoint = null;

        AddDomainEvent(new EmbeddedEngineErrorEvent(Id, Name, Type, errorMessage));
    }

    /// <summary>
    /// Reinicia a engine embarcada
    /// </summary>
    public void Restart(int newProcessId)
    {
        if (Status == EngineStatus.Running)
        {
            Stop();
        }

        // Aguarda um momento para garantir que a engine parou
        Status = EngineStatus.Configured;
        Start(newProcessId);
    }

    /// <summary>
    /// Verifica se a engine pode ser iniciada
    /// </summary>
    public bool CanStart()
    {
        return IsConfigured && 
               (Status == EngineStatus.Configured || 
                Status == EngineStatus.Stopped || 
                Status == EngineStatus.Error);
    }

    /// <summary>
    /// Verifica se a engine pode ser parada
    /// </summary>
    public bool CanStop()
    {
        return Status == EngineStatus.Running;
    }

    /// <summary>
    /// Obtém eventos de domínio
    /// </summary>
    public IReadOnlyCollection<DomainEvent> GetDomainEvents() => _domainEvents.AsReadOnly();

    /// <summary>
    /// Limpa eventos de domínio
    /// </summary>
    public void ClearDomainEvents() => _domainEvents.Clear();

    /// <summary>
    /// Adiciona evento de domínio
    /// </summary>
    private void AddDomainEvent(DomainEvent domainEvent) => _domainEvents.Add(domainEvent);
}
