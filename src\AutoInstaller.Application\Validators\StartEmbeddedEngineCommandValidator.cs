using AutoInstaller.Application.Commands;
using AutoInstaller.Core.Enums;
using FluentValidation;

namespace AutoInstaller.Application.Validators;

/// <summary>
/// Validator para comando de iniciar engine embarcada
/// Implementa validações rigorosas para sistema autônomo
/// </summary>
public class StartEmbeddedEngineCommandValidator : AbstractValidator<StartEmbeddedEngineCommand>
{
    public StartEmbeddedEngineCommandValidator()
    {
        RuleFor(x => x.EngineName)
            .NotEmpty()
            .WithMessage("Nome da engine é obrigatório")
            .Length(1, 50)
            .WithMessage("Nome da engine deve ter entre 1 e 50 caracteres")
            .Matches("^[a-zA-Z0-9_-]+$")
            .WithMessage("Nome da engine deve conter apenas letras, números, underscore e hífen");

        RuleFor(x => x.EngineType)
            .IsInEnum()
            .WithMessage("Tipo de engine inválido")
            .Must(BeValidEngineType)
            .WithMessage("Tipo de engine deve ser Docker ou Podman");

        RuleFor(x => x.BinaryPath)
            .NotEmpty()
            .WithMessage("Caminho dos binários é obrigatório")
            .Must(BeValidPath)
            .WithMessage("Caminho dos binários deve ser um caminho válido");

        RuleFor(x => x.Version)
            .NotEmpty()
            .WithMessage("Versão da engine é obrigatória")
            .Matches(@"^\d+\.\d+(\.\d+)?(-\w+)?$")
            .WithMessage("Versão deve estar no formato semântico (ex: 1.0.0, 2.1.3-beta)");

        RuleFor(x => x.OperatingSystem)
            .IsInEnum()
            .WithMessage("Sistema operacional inválido")
            .Must(BeSupportedOperatingSystem)
            .WithMessage("Sistema operacional deve ser Windows ou Linux");

        RuleFor(x => x.Architecture)
            .NotEmpty()
            .WithMessage("Arquitetura é obrigatória")
            .Must(BeSupportedArchitecture)
            .WithMessage("Arquitetura deve ser x64 ou arm64");

        RuleFor(x => x.TimeoutSeconds)
            .GreaterThan(0)
            .WithMessage("Timeout deve ser maior que zero")
            .LessThanOrEqualTo(300)
            .WithMessage("Timeout não pode ser maior que 300 segundos (5 minutos)");

        // Validações condicionais baseadas no tipo de engine
        When(x => x.EngineType == EngineType.Docker, () =>
        {
            RuleFor(x => x.EngineName)
                .Must(BeValidDockerEngineName)
                .WithMessage("Nome da engine Docker deve começar com 'docker'");
        });

        When(x => x.EngineType == EngineType.Podman, () =>
        {
            RuleFor(x => x.EngineName)
                .Must(BeValidPodmanEngineName)
                .WithMessage("Nome da engine Podman deve começar com 'podman'");
        });

        // Validações específicas por sistema operacional
        When(x => x.OperatingSystem == OperatingSystem.Windows, () =>
        {
            RuleFor(x => x.BinaryPath)
                .Must(BeValidWindowsPath)
                .WithMessage("Caminho deve ser válido para Windows");
        });

        When(x => x.OperatingSystem == OperatingSystem.Linux, () =>
        {
            RuleFor(x => x.BinaryPath)
                .Must(BeValidLinuxPath)
                .WithMessage("Caminho deve ser válido para Linux");
        });
    }

    private static bool BeValidEngineType(EngineType engineType)
    {
        return engineType == EngineType.Docker || engineType == EngineType.Podman;
    }

    private static bool BeSupportedOperatingSystem(OperatingSystem operatingSystem)
    {
        return operatingSystem == OperatingSystem.Windows || 
               operatingSystem == OperatingSystem.Linux;
    }

    private static bool BeSupportedArchitecture(string architecture)
    {
        if (string.IsNullOrWhiteSpace(architecture))
            return false;

        var supportedArchitectures = new[] { "x64", "x86_64", "amd64", "arm64", "aarch64" };
        return supportedArchitectures.Contains(architecture.ToLowerInvariant());
    }

    private static bool BeValidPath(string path)
    {
        if (string.IsNullOrWhiteSpace(path))
            return false;

        try
        {
            // Verificar se é um caminho válido
            var fullPath = Path.GetFullPath(path);
            return !string.IsNullOrWhiteSpace(fullPath);
        }
        catch
        {
            return false;
        }
    }

    private static bool BeValidWindowsPath(string path)
    {
        if (!BeValidPath(path))
            return false;

        // Verificar padrões específicos do Windows
        return path.Length >= 3 && 
               char.IsLetter(path[0]) && 
               path[1] == ':' && 
               (path[2] == '\\' || path[2] == '/');
    }

    private static bool BeValidLinuxPath(string path)
    {
        if (!BeValidPath(path))
            return false;

        // Verificar padrões específicos do Linux
        return path.StartsWith('/') || path.StartsWith("./") || path.StartsWith("../");
    }

    private static bool BeValidDockerEngineName(string engineName)
    {
        if (string.IsNullOrWhiteSpace(engineName))
            return false;

        return engineName.ToLowerInvariant().StartsWith("docker");
    }

    private static bool BeValidPodmanEngineName(string engineName)
    {
        if (string.IsNullOrWhiteSpace(engineName))
            return false;

        return engineName.ToLowerInvariant().StartsWith("podman");
    }
}

/// <summary>
/// Validator para comando de parar engine embarcada
/// </summary>
public class StopEmbeddedEngineCommandValidator : AbstractValidator<StopEmbeddedEngineCommand>
{
    public StopEmbeddedEngineCommandValidator()
    {
        RuleFor(x => x.EngineId)
            .NotEmpty()
            .WithMessage("ID da engine é obrigatório")
            .Must(BeValidGuid)
            .WithMessage("ID da engine deve ser um GUID válido");

        RuleFor(x => x.TimeoutSeconds)
            .GreaterThan(0)
            .WithMessage("Timeout deve ser maior que zero")
            .LessThanOrEqualTo(120)
            .WithMessage("Timeout não pode ser maior que 120 segundos (2 minutos)");

        // Se nome da engine for fornecido, validar
        When(x => !string.IsNullOrWhiteSpace(x.EngineName), () =>
        {
            RuleFor(x => x.EngineName)
                .Length(1, 50)
                .WithMessage("Nome da engine deve ter entre 1 e 50 caracteres")
                .Matches("^[a-zA-Z0-9_-]+$")
                .WithMessage("Nome da engine deve conter apenas letras, números, underscore e hífen");
        });
    }

    private static bool BeValidGuid(string guidString)
    {
        return Guid.TryParse(guidString, out _);
    }
}

/// <summary>
/// Validator para comando de criar container
/// </summary>
public class CreateContainerCommandValidator : AbstractValidator<CreateContainerCommand>
{
    public CreateContainerCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Nome do container é obrigatório")
            .Length(1, 63)
            .WithMessage("Nome do container deve ter entre 1 e 63 caracteres")
            .Matches("^[a-zA-Z0-9][a-zA-Z0-9_.-]*$")
            .WithMessage("Nome do container deve começar com letra/número e conter apenas letras, números, underscore, ponto e hífen");

        RuleFor(x => x.ImageName)
            .NotEmpty()
            .WithMessage("Nome da imagem é obrigatório")
            .Must(BeValidImageName)
            .WithMessage("Nome da imagem deve estar em formato válido (ex: nginx, ubuntu:20.04, registry.com/image:tag)");

        RuleFor(x => x.ImageTag)
            .NotEmpty()
            .WithMessage("Tag da imagem é obrigatória")
            .Matches("^[a-zA-Z0-9_.-]+$")
            .WithMessage("Tag da imagem deve conter apenas letras, números, underscore, ponto e hífen");

        RuleFor(x => x.EmbeddedEngineId)
            .NotEmpty()
            .WithMessage("ID da engine embarcada é obrigatório")
            .Must(BeValidGuid)
            .WithMessage("ID da engine embarcada deve ser um GUID válido");

        RuleFor(x => x.RestartPolicy)
            .IsInEnum()
            .WithMessage("Política de restart inválida");

        // Validar portas se fornecidas
        RuleForEach(x => x.Ports)
            .Must(BeValidPortMapping)
            .WithMessage("Mapeamento de porta deve estar no formato 'hostPort:containerPort' ou 'hostPort:containerPort/protocol'");

        // Validar volumes se fornecidos
        RuleForEach(x => x.Volumes)
            .Must(BeValidVolumeMount)
            .WithMessage("Montagem de volume deve estar no formato 'hostPath:containerPath' ou 'hostPath:containerPath:mode'");

        // Validar variáveis de ambiente
        RuleFor(x => x.Environment)
            .Must(HaveValidEnvironmentVariables)
            .WithMessage("Variáveis de ambiente devem ter nomes válidos (apenas letras, números e underscore)");

        // Validar comando se fornecido
        When(x => !string.IsNullOrWhiteSpace(x.Command), () =>
        {
            RuleFor(x => x.Command)
                .Length(1, 1000)
                .WithMessage("Comando deve ter entre 1 e 1000 caracteres");
        });

        // Validar diretório de trabalho se fornecido
        When(x => !string.IsNullOrWhiteSpace(x.WorkingDirectory), () =>
        {
            RuleFor(x => x.WorkingDirectory)
                .Must(BeValidPath)
                .WithMessage("Diretório de trabalho deve ser um caminho válido");
        });
    }

    private static bool BeValidGuid(string guidString)
    {
        return Guid.TryParse(guidString, out _);
    }

    private static bool BeValidImageName(string imageName)
    {
        if (string.IsNullOrWhiteSpace(imageName))
            return false;

        // Padrões válidos:
        // - nginx
        // - ubuntu:20.04
        // - registry.com/namespace/image:tag
        // - localhost:5000/image:tag
        
        var parts = imageName.Split(':');
        if (parts.Length > 2)
            return false;

        var namepart = parts[0];
        
        // Verificar se contém apenas caracteres válidos
        return System.Text.RegularExpressions.Regex.IsMatch(namepart, 
            @"^[a-z0-9]+(?:[._-][a-z0-9]+)*(?:/[a-z0-9]+(?:[._-][a-z0-9]+)*)*$");
    }

    private static bool BeValidPortMapping(string portMapping)
    {
        if (string.IsNullOrWhiteSpace(portMapping))
            return false;

        // Formatos válidos:
        // - 8080:80
        // - 8080:80/tcp
        // - 127.0.0.1:8080:80
        // - 127.0.0.1:8080:80/udp

        var parts = portMapping.Split(':');
        if (parts.Length < 2 || parts.Length > 3)
            return false;

        // Verificar se as portas são números válidos
        var hostPortPart = parts.Length == 3 ? parts[1] : parts[0];
        var containerPortPart = parts[parts.Length - 1];

        // Remover protocolo se presente
        if (containerPortPart.Contains('/'))
        {
            var protocolParts = containerPortPart.Split('/');
            if (protocolParts.Length != 2)
                return false;
            
            containerPortPart = protocolParts[0];
            var protocol = protocolParts[1].ToLowerInvariant();
            if (protocol != "tcp" && protocol != "udp")
                return false;
        }

        return int.TryParse(hostPortPart, out var hostPort) &&
               int.TryParse(containerPortPart, out var containerPort) &&
               hostPort > 0 && hostPort <= 65535 &&
               containerPort > 0 && containerPort <= 65535;
    }

    private static bool BeValidVolumeMount(string volumeMount)
    {
        if (string.IsNullOrWhiteSpace(volumeMount))
            return false;

        var parts = volumeMount.Split(':');
        if (parts.Length < 2 || parts.Length > 3)
            return false;

        // Verificar se os caminhos não estão vazios
        return !string.IsNullOrWhiteSpace(parts[0]) && 
               !string.IsNullOrWhiteSpace(parts[1]);
    }

    private static bool HaveValidEnvironmentVariables(Dictionary<string, string> environment)
    {
        if (environment == null)
            return true;

        foreach (var kvp in environment)
        {
            // Nome da variável deve conter apenas letras, números e underscore
            if (!System.Text.RegularExpressions.Regex.IsMatch(kvp.Key, @"^[a-zA-Z_][a-zA-Z0-9_]*$"))
                return false;
        }

        return true;
    }

    private static bool BeValidPath(string path)
    {
        if (string.IsNullOrWhiteSpace(path))
            return false;

        try
        {
            var fullPath = Path.GetFullPath(path);
            return !string.IsNullOrWhiteSpace(fullPath);
        }
        catch
        {
            return false;
        }
    }
}
