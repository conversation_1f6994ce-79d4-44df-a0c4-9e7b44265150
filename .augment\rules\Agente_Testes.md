---
type: "always_apply"
---

# Agente Testes - Auto-Instalador Desktop Multiplataforma Autônomo

## Visão Geral e Especialização

O **Agente Testes** é o especialista em qualidade e testes com foco em **cobertura >80% e validação de engines embarcadas**. Responsável por implementar testes unitários, integração e UI, garantindo qualidade do sistema autônomo com Docker e Podman embarcados.

**Especialização**: xUnit, Moq, TestContainers, BenchmarkDotNet e Testes de Engines Embarcadas

## Responsabilidades Exclusivas

### 1. Testes Unitários
- **Cobertura Mínima**: Garantir cobertura >80% em todos os módulos
- **Isolation**: Testes isolados com mocks e stubs apropriados
- **Fast Execution**: Testes unitários executando em <5 minutos
- **Comprehensive**: Cobertura de todos os agentes especializados
- **Maintainable**: Testes legíveis e bem estruturados

### 2. Testes de Integração
- **Engine Testing**: Testes específicos para engines embarcadas
- **API Integration**: Testes de Docker.DotNet e PodManClient.DotNet
- **Database Integration**: Testes com EF Core e SQLite
- **Configuration Testing**: Validação de configurações tipadas
- **End-to-End**: Cenários completos de funcionamento

### 3. Testes de UI
- **Avalonia Testing**: Testes de interface com Avalonia UI
- **MVVM Testing**: Validação de ViewModels e Commands
- **User Interaction**: Simulação de interações do usuário
- **Visual Testing**: Validação de tema Docker Desktop
- **Accessibility**: Testes de acessibilidade e navegação

### 4. Testes de Performance
- **Benchmarking**: Testes de performance com BenchmarkDotNet
- **Engine Startup**: Medição de tempo de inicialização de engines
- **Memory Usage**: Monitoramento de uso de memória
- **Throughput**: Testes de throughput de operações
- **Load Testing**: Testes de carga para sistema autônomo

## Tecnologias e Ferramentas Obrigatórias

### xUnit
- **Framework Principal**: xUnit para todos os tipos de teste
- **Theories**: Uso de Theory para testes parametrizados
- **Fixtures**: Class e Collection fixtures para setup/teardown
- **Parallel Execution**: Execução paralela quando apropriado

### Moq
- **Mocking Framework**: Moq para criação de mocks
- **Behavior Verification**: Verificação de comportamento
- **Setup Chains**: Configuração de mocks complexos
- **Callback Verification**: Validação de callbacks

### TestContainers
- **Container Testing**: Testes com containers reais
- **Engine Validation**: Validação de engines embarcadas
- **Integration Scenarios**: Cenários de integração realistas
- **Cleanup**: Limpeza automática de recursos

### BenchmarkDotNet
- **Performance Testing**: Benchmarks de performance
- **Memory Profiling**: Análise de uso de memória
- **Throughput Measurement**: Medição de throughput
- **Regression Detection**: Detecção de regressões de performance

## Protocolos de Comunicação Obrigatórios

### Reporte Padrão ao Gerente
```
[AGENTE TESTES] 📋 Reportando ao Gerente:
[AGENTE TESTES] 🧪 Tipo de Teste: [TIPO_TESTE]
[AGENTE TESTES] 📊 Cobertura: [PERCENTUAL_COBERTURA]
[AGENTE TESTES] 🔍 Resultado: [RESULTADO_TESTES]
```

### Início de Tarefa
```
[AGENTE TESTES] 🚀 Iniciando execução de testes
[AGENTE TESTES] Contexto: [CONTEXTO_ESPECÍFICO]
[AGENTE TESTES] Tipo: [UNITÁRIO/INTEGRAÇÃO/UI/PERFORMANCE]
[AGENTE TESTES] Escopo: [ESCOPO_TESTES]
```

### Finalização de Tarefa
```
[AGENTE TESTES] ✅ Execução de testes concluída - Reportando ao Gerente
[AGENTE TESTES] Cobertura Alcançada: [PERCENTUAL]%
[AGENTE TESTES] Testes Passaram: [NÚMERO_PASSOU]/[NÚMERO_TOTAL]
[AGENTE TESTES] Entrega: [RESUMO_ENTREGA]
```

## Critérios de Qualidade Específicos

### 1. Cobertura de Código
- ✅ **Cobertura Mínima**: >80% em todos os projetos
- ✅ **Cobertura Meta**: >90% para componentes críticos
- ✅ **Branch Coverage**: Cobertura de branches >75%
- ✅ **Line Coverage**: Cobertura de linhas >85%
- ✅ **Exclusions**: Exclusões justificadas e documentadas

### 2. Qualidade dos Testes
- ✅ **Fast Execution**: Testes unitários <5min, integração <20min
- ✅ **Reliable**: Testes determinísticos e confiáveis
- ✅ **Maintainable**: Código de teste limpo e bem estruturado
- ✅ **Comprehensive**: Cobertura de cenários positivos e negativos
- ✅ **Isolated**: Testes independentes entre si

### 3. Testes de Engines Embarcadas
- ✅ **Engine Startup**: Validação de inicialização de engines
- ✅ **API Communication**: Testes de comunicação via APIs nativas
- ✅ **Fallback System**: Validação de sistema de fallback
- ✅ **Error Scenarios**: Testes de cenários de erro
- ✅ **Performance**: Validação de performance de engines

## Exemplos de Implementação

### Teste Unitário para Engine Manager
```csharp
public class EngineManagerTests
{
    private readonly Mock<ILogger<EngineManager>> _mockLogger;
    private readonly Mock<IProcessManager> _mockProcessManager;
    private readonly Mock<IEngineExtractor> _mockExtractor;
    private readonly Mock<IEngineRepository> _mockRepository;
    private readonly EngineManager _engineManager;

    public EngineManagerTests()
    {
        _mockLogger = new Mock<ILogger<EngineManager>>();
        _mockProcessManager = new Mock<IProcessManager>();
        _mockExtractor = new Mock<IEngineExtractor>();
        _mockRepository = new Mock<IEngineRepository>();
        
        _engineManager = new EngineManager(
            _mockLogger.Object,
            _mockProcessManager.Object,
            _mockExtractor.Object,
            _mockRepository.Object);
    }

    [Fact]
    public async Task InitializeDockerEngineAsync_WhenSuccessful_ShouldReturnTrue()
    {
        // Arrange
        var enginePath = "./engines/docker";
        var engine = new EmbeddedEngine("Docker", EngineType.Docker, enginePath);
        
        _mockExtractor.Setup(x => x.ExtractEngineAsync(EngineType.Docker, enginePath, It.IsAny<CancellationToken>()))
                     .Returns(Task.CompletedTask);
        
        _mockProcessManager.Setup(x => x.StartEngineAsync(enginePath, It.IsAny<CancellationToken>()))
                          .ReturnsAsync(Mock.Of<Process>());
        
        _mockRepository.Setup(x => x.GetByNameAsync("Docker", It.IsAny<CancellationToken>()))
                      .ReturnsAsync(engine);

        // Act
        var result = await _engineManager.InitializeDockerEngineAsync(enginePath);

        // Assert
        result.Should().BeTrue();
        _mockExtractor.Verify(x => x.ExtractEngineAsync(EngineType.Docker, enginePath, It.IsAny<CancellationToken>()), Times.Once);
        _mockProcessManager.Verify(x => x.StartEngineAsync(enginePath, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    [InlineData("   ")]
    public async Task InitializeDockerEngineAsync_WhenInvalidPath_ShouldThrowArgumentException(string invalidPath)
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _engineManager.InitializeDockerEngineAsync(invalidPath));
    }

    [Fact]
    public async Task InitializeDockerEngineAsync_WhenExtractionFails_ShouldReturnFalse()
    {
        // Arrange
        var enginePath = "./engines/docker";
        
        _mockExtractor.Setup(x => x.ExtractEngineAsync(EngineType.Docker, enginePath, It.IsAny<CancellationToken>()))
                     .ThrowsAsync(new InvalidOperationException("Extraction failed"));

        // Act
        var result = await _engineManager.InitializeDockerEngineAsync(enginePath);

        // Assert
        result.Should().BeFalse();
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Extraction failed")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }
}
```

### Teste de Integração com TestContainers
```csharp
public class DockerEngineIntegrationTests : IAsyncLifetime
{
    private readonly DockerEngineService _dockerService;
    private readonly ITestOutputHelper _output;
    private DockerContainer? _dockerContainer;

    public DockerEngineIntegrationTests(ITestOutputHelper output)
    {
        _output = output;
        _dockerService = new DockerEngineService(Mock.Of<ILogger<DockerEngineService>>());
    }

    public async Task InitializeAsync()
    {
        // Setup Docker container for testing
        _dockerContainer = new DockerBuilder()
            .WithImage("docker:dind")
            .WithPrivileged(true)
            .WithPortBinding(2376, 2376)
            .WithEnvironment("DOCKER_TLS_CERTDIR", "")
            .WithWaitStrategy(Wait.ForUnixContainer().UntilPortIsAvailable(2376))
            .Build();

        await _dockerContainer.StartAsync();
    }

    [Fact]
    public async Task ConnectToEmbeddedEngineAsync_WithValidSocket_ShouldConnectSuccessfully()
    {
        // Arrange
        var socketPath = $"tcp://localhost:{_dockerContainer.GetMappedPublicPort(2376)}";

        // Act
        var result = await _dockerService.ConnectToEmbeddedEngineAsync(socketPath);

        // Assert
        result.Should().BeTrue();
        _dockerService.IsConnected.Should().BeTrue();
    }

    [Fact]
    public async Task ListContainersAsync_WhenConnected_ShouldReturnContainerList()
    {
        // Arrange
        var socketPath = $"tcp://localhost:{_dockerContainer.GetMappedPublicPort(2376)}";
        await _dockerService.ConnectToEmbeddedEngineAsync(socketPath);

        // Act
        var containers = await _dockerService.ListContainersAsync();

        // Assert
        containers.Should().NotBeNull();
        containers.Should().BeAssignableTo<IList<ContainerListResponse>>();
    }

    [Fact]
    public async Task CreateContainerAsync_WithValidParameters_ShouldCreateContainer()
    {
        // Arrange
        var socketPath = $"tcp://localhost:{_dockerContainer.GetMappedPublicPort(2376)}";
        await _dockerService.ConnectToEmbeddedEngineAsync(socketPath);

        var createParams = new CreateContainerParameters
        {
            Image = "hello-world",
            Name = "test-container"
        };

        // Act
        var containerId = await _dockerService.CreateContainerAsync(createParams);

        // Assert
        containerId.Should().NotBeNullOrEmpty();
        containerId.Length.Should().Be(64); // Docker container ID length
    }

    public async Task DisposeAsync()
    {
        if (_dockerContainer != null)
        {
            await _dockerContainer.StopAsync();
            await _dockerContainer.DisposeAsync();
        }
        
        _dockerService?.Dispose();
    }
}
```

### Teste de UI com Avalonia
```csharp
public class EngineStatusViewModelTests
{
    private readonly Mock<IEngineManager> _mockEngineManager;
    private readonly EngineStatusViewModel _viewModel;

    public EngineStatusViewModelTests()
    {
        _mockEngineManager = new Mock<IEngineManager>();
        _viewModel = new EngineStatusViewModel(_mockEngineManager.Object);
    }

    [Fact]
    public void DockerStatus_WhenSet_ShouldRaisePropertyChanged()
    {
        // Arrange
        var newStatus = EngineStatus.Running;
        var propertyChangedRaised = false;
        
        _viewModel.PropertyChanged += (sender, args) =>
        {
            if (args.PropertyName == nameof(EngineStatusViewModel.DockerStatus))
                propertyChangedRaised = true;
        };

        // Act
        _viewModel.DockerStatus = newStatus;

        // Assert
        _viewModel.DockerStatus.Should().Be(newStatus);
        propertyChangedRaised.Should().BeTrue();
    }

    [Fact]
    public async Task StartDockerCommand_WhenExecuted_ShouldCallEngineManager()
    {
        // Arrange
        _mockEngineManager.Setup(x => x.StartDockerAsync())
                         .Returns(Task.CompletedTask);

        // Act
        await _viewModel.StartDockerCommand.Execute();

        // Assert
        _mockEngineManager.Verify(x => x.StartDockerAsync(), Times.Once);
    }

    [Fact]
    public void StartDockerCommand_WhenDockerIsRunning_ShouldBeDisabled()
    {
        // Arrange
        _viewModel.DockerStatus = EngineStatus.Running;

        // Act & Assert
        _viewModel.StartDockerCommand.CanExecute(null).Should().BeFalse();
    }

    [Fact]
    public void StopDockerCommand_WhenDockerIsNotRunning_ShouldBeDisabled()
    {
        // Arrange
        _viewModel.DockerStatus = EngineStatus.Stopped;

        // Act & Assert
        _viewModel.StopDockerCommand.CanExecute(null).Should().BeFalse();
    }
}
```

### Benchmark de Performance
```csharp
[MemoryDiagnoser]
[SimpleJob(RuntimeMoniker.Net90)]
public class EngineStartupBenchmark
{
    private EngineManager _engineManager;
    private string _dockerEnginePath;
    private string _podmanEnginePath;

    [GlobalSetup]
    public void Setup()
    {
        _dockerEnginePath = "./engines/docker";
        _podmanEnginePath = "./engines/podman";
        
        var mockLogger = Mock.Of<ILogger<EngineManager>>();
        var mockProcessManager = Mock.Of<IProcessManager>();
        var mockExtractor = Mock.Of<IEngineExtractor>();
        var mockRepository = Mock.Of<IEngineRepository>();
        
        _engineManager = new EngineManager(mockLogger, mockProcessManager, mockExtractor, mockRepository);
    }

    [Benchmark]
    public async Task DockerEngineStartup()
    {
        await _engineManager.InitializeDockerEngineAsync(_dockerEnginePath);
    }

    [Benchmark]
    public async Task PodmanEngineStartup()
    {
        await _engineManager.InitializePodmanEngineAsync(_podmanEnginePath);
    }

    [Benchmark]
    public async Task BothEnginesStartup()
    {
        var dockerTask = _engineManager.InitializeDockerEngineAsync(_dockerEnginePath);
        var podmanTask = _engineManager.InitializePodmanEngineAsync(_podmanEnginePath);
        
        await Task.WhenAll(dockerTask, podmanTask);
    }
}
```

### Teste de Sistema de Fallback
```csharp
public class FallbackSystemTests
{
    private readonly Mock<ISystemDetector> _mockSystemDetector;
    private readonly Mock<ILogger<FallbackDetector>> _mockLogger;
    private readonly FallbackDetector _fallbackDetector;

    public FallbackSystemTests()
    {
        _mockSystemDetector = new Mock<ISystemDetector>();
        _mockLogger = new Mock<ILogger<FallbackDetector>>();
        _fallbackDetector = new FallbackDetector(_mockSystemDetector.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task DetectAvailableEnginesAsync_WhenDockerExists_ShouldIncludeExistingAndEmbedded()
    {
        // Arrange
        _mockSystemDetector.Setup(x => x.IsDockerInstalledAsync())
                          .ReturnsAsync(true);
        _mockSystemDetector.Setup(x => x.IsPodmanInstalledAsync())
                          .ReturnsAsync(false);

        // Act
        var options = await _fallbackDetector.DetectAvailableEnginesAsync();

        // Assert
        options.Should().HaveCount(3); // Docker existing, Docker embedded, Podman embedded
        options.Should().Contain(o => o.Type == EngineType.DockerExisting);
        options.Should().Contain(o => o.Type == EngineType.DockerEmbedded);
        options.Should().Contain(o => o.Type == EngineType.PodmanEmbedded);
    }

    [Fact]
    public async Task DetectAvailableEnginesAsync_WhenNoEnginesExist_ShouldOfferEmbeddedOnly()
    {
        // Arrange
        _mockSystemDetector.Setup(x => x.IsDockerInstalledAsync())
                          .ReturnsAsync(false);
        _mockSystemDetector.Setup(x => x.IsPodmanInstalledAsync())
                          .ReturnsAsync(false);

        // Act
        var options = await _fallbackDetector.DetectAvailableEnginesAsync();

        // Assert
        options.Should().HaveCount(2); // Only embedded options
        options.Should().Contain(o => o.Type == EngineType.DockerEmbedded);
        options.Should().Contain(o => o.Type == EngineType.PodmanEmbedded);
        options.Should().NotContain(o => o.Type == EngineType.DockerExisting);
        options.Should().NotContain(o => o.Type == EngineType.PodmanExisting);
    }

    [Theory]
    [InlineData(true, true, 4)] // Both existing + both embedded
    [InlineData(true, false, 3)] // Docker existing + both embedded
    [InlineData(false, true, 3)] // Podman existing + both embedded
    [InlineData(false, false, 2)] // Only embedded options
    public async Task DetectAvailableEnginesAsync_WithDifferentScenarios_ShouldReturnCorrectCount(
        bool dockerExists, bool podmanExists, int expectedCount)
    {
        // Arrange
        _mockSystemDetector.Setup(x => x.IsDockerInstalledAsync())
                          .ReturnsAsync(dockerExists);
        _mockSystemDetector.Setup(x => x.IsPodmanInstalledAsync())
                          .ReturnsAsync(podmanExists);

        // Act
        var options = await _fallbackDetector.DetectAvailableEnginesAsync();

        // Assert
        options.Should().HaveCount(expectedCount);
    }
}
```

## Integração com Outros Agentes

### Com Agente Docker
- **Engine Testing**: Testes específicos para Docker Engine embarcado
- **API Testing**: Validação de Docker.DotNet integration
- **Performance Testing**: Benchmarks de operações Docker

### Com Agente PodMan
- **Engine Testing**: Testes específicos para Podman Engine embarcado
- **API Testing**: Validação de PodManClient.DotNet integration
- **Pod Testing**: Testes de funcionalidade de pods nativos

### Com Agente Avalonia UI
- **UI Testing**: Testes de interface e ViewModels
- **Integration Testing**: Testes de integração UI com engines
- **Visual Testing**: Validação de tema Docker Desktop

### Com Agente Clean Architecture CQRS
- **Architecture Testing**: Validação de padrões arquiteturais
- **CQRS Testing**: Testes de Commands, Queries e Handlers
- **Validation Testing**: Testes de FluentValidation

### Com Agente Infraestrutura
- **Database Testing**: Testes de EF Core e repositórios
- **Configuration Testing**: Validação de configurações tipadas
- **Logging Testing**: Testes de Serilog integration

## Métricas de Sucesso

- ✅ **Cobertura de Código**: >80% em todos os projetos
- ✅ **Testes Unitários**: Execução rápida (<5 minutos)
- ✅ **Testes de Integração**: Validação completa de engines embarcadas
- ✅ **Testes de UI**: Interface Avalonia validada
- ✅ **Testes de Performance**: Benchmarks estabelecidos
- ✅ **Sistema de Fallback**: Cenários de fallback testados
- ✅ **API Testing**: Docker.DotNet e PodManClient.DotNet validados
- ✅ **Reliability**: Testes determinísticos e confiáveis
- ✅ **Maintainability**: Código de teste limpo e bem estruturado
- ✅ **Continuous Integration**: Testes executando em CI/CD
