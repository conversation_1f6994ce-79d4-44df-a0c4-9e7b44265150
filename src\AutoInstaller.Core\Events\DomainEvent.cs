using AutoInstaller.Core.Enums;

namespace AutoInstaller.Core.Events;

/// <summary>
/// Classe base para eventos de domínio
/// Implementa padrões DDD para comunicação entre agregados
/// </summary>
public abstract record DomainEvent
{
    /// <summary>
    /// ID único do evento
    /// </summary>
    public Guid Id { get; } = Guid.NewGuid();

    /// <summary>
    /// Timestamp do evento
    /// </summary>
    public DateTime OccurredAt { get; } = DateTime.UtcNow;

    /// <summary>
    /// Versão do evento (para versionamento)
    /// </summary>
    public int Version { get; init; } = 1;
}

/// <summary>
/// Evento disparado quando engine embarcada é criada
/// </summary>
public record EmbeddedEngineCreatedEvent(
    Guid EngineId,
    string EngineName,
    EngineType EngineType,
    string Version
) : DomainEvent;

/// <summary>
/// Evento disparado quando engine embarcada é extraída
/// </summary>
public record EmbeddedEngineExtractedEvent(
    Guid EngineId,
    string EngineName,
    EngineType EngineType,
    string ExtractedPath
) : DomainEvent;

/// <summary>
/// Evento disparado quando engine embarcada é configurada
/// </summary>
public record EmbeddedEngineConfiguredEvent(
    Guid EngineId,
    string EngineName,
    EngineType EngineType,
    string CommunicationEndpoint
) : DomainEvent;

/// <summary>
/// Evento disparado quando engine embarcada está iniciando
/// </summary>
public record EmbeddedEngineStartingEvent(
    Guid EngineId,
    string EngineName,
    EngineType EngineType,
    int ProcessId
) : DomainEvent;

/// <summary>
/// Evento disparado quando engine embarcada está em execução
/// </summary>
public record EmbeddedEngineRunningEvent(
    Guid EngineId,
    string EngineName,
    EngineType EngineType,
    int ProcessId
) : DomainEvent;

/// <summary>
/// Evento disparado quando engine embarcada está parando
/// </summary>
public record EmbeddedEngineStoppingEvent(
    Guid EngineId,
    string EngineName,
    EngineType EngineType,
    int ProcessId
) : DomainEvent;

/// <summary>
/// Evento disparado quando engine embarcada foi parada
/// </summary>
public record EmbeddedEngineStoppedEvent(
    Guid EngineId,
    string EngineName,
    EngineType EngineType
) : DomainEvent;

/// <summary>
/// Evento disparado quando engine embarcada tem erro
/// </summary>
public record EmbeddedEngineErrorEvent(
    Guid EngineId,
    string EngineName,
    EngineType EngineType,
    string ErrorMessage
) : DomainEvent;

/// <summary>
/// Evento disparado quando container é criado
/// </summary>
public record ContainerCreatedEvent(
    Guid ContainerId,
    string ContainerName,
    string ImageName,
    string ImageTag,
    EngineType EngineType
) : DomainEvent;

/// <summary>
/// Evento disparado quando ID do container na engine é definido
/// </summary>
public record ContainerEngineIdSetEvent(
    Guid ContainerId,
    string ContainerName,
    string EngineContainerId,
    EngineType EngineType
) : DomainEvent;

/// <summary>
/// Evento disparado quando container está iniciando
/// </summary>
public record ContainerStartingEvent(
    Guid ContainerId,
    string ContainerName,
    EngineType EngineType
) : DomainEvent;

/// <summary>
/// Evento disparado quando container está em execução
/// </summary>
public record ContainerRunningEvent(
    Guid ContainerId,
    string ContainerName,
    EngineType EngineType
) : DomainEvent;

/// <summary>
/// Evento disparado quando container está parando
/// </summary>
public record ContainerStoppingEvent(
    Guid ContainerId,
    string ContainerName,
    EngineType EngineType
) : DomainEvent;

/// <summary>
/// Evento disparado quando container foi parado
/// </summary>
public record ContainerStoppedEvent(
    Guid ContainerId,
    string ContainerName,
    EngineType EngineType,
    int? ExitCode
) : DomainEvent;

/// <summary>
/// Evento disparado quando container é pausado
/// </summary>
public record ContainerPausedEvent(
    Guid ContainerId,
    string ContainerName,
    EngineType EngineType
) : DomainEvent;

/// <summary>
/// Evento disparado quando container é resumido
/// </summary>
public record ContainerResumedEvent(
    Guid ContainerId,
    string ContainerName,
    EngineType EngineType
) : DomainEvent;

/// <summary>
/// Evento disparado quando container tem erro
/// </summary>
public record ContainerErrorEvent(
    Guid ContainerId,
    string ContainerName,
    EngineType EngineType,
    string ErrorMessage
) : DomainEvent;

/// <summary>
/// Evento disparado quando sistema de fallback é ativado
/// </summary>
public record FallbackSystemActivatedEvent(
    EngineType EngineType,
    FallbackStrategy Strategy,
    string Reason
) : DomainEvent;

/// <summary>
/// Evento disparado quando engine existente é detectada
/// </summary>
public record ExistingEngineDetectedEvent(
    EngineType EngineType,
    string EnginePath,
    string Version,
    bool IsRunning
) : DomainEvent;

/// <summary>
/// Evento disparado quando usuário escolhe engine
/// </summary>
public record EngineSelectionMadeEvent(
    EngineType EngineType,
    bool UseEmbedded,
    string SelectedEnginePath,
    string Reason
) : DomainEvent;

/// <summary>
/// Evento disparado quando health check falha
/// </summary>
public record HealthCheckFailedEvent(
    Guid EngineId,
    string EngineName,
    EngineType EngineType,
    string HealthCheckType,
    string ErrorMessage
) : DomainEvent;

/// <summary>
/// Evento disparado quando health check é bem-sucedido
/// </summary>
public record HealthCheckSucceededEvent(
    Guid EngineId,
    string EngineName,
    EngineType EngineType,
    string HealthCheckType,
    TimeSpan ResponseTime
) : DomainEvent;
