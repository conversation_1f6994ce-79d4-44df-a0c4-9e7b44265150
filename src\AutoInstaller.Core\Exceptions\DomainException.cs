namespace AutoInstaller.Core.Exceptions;

/// <summary>
/// Exceção base para erros de domínio
/// Representa violações de regras de negócio
/// </summary>
public class DomainException : Exception
{
    /// <summary>
    /// Código do erro (para categorização)
    /// </summary>
    public string ErrorCode { get; }

    /// <summary>
    /// Propriedades adicionais do erro
    /// </summary>
    public Dictionary<string, object> Properties { get; }

    /// <summary>
    /// Construtor básico
    /// </summary>
    public DomainException(string message) : base(message)
    {
        ErrorCode = "DOMAIN_ERROR";
        Properties = new Dictionary<string, object>();
    }

    /// <summary>
    /// Construtor com código de erro
    /// </summary>
    public DomainException(string message, string errorCode) : base(message)
    {
        ErrorCode = errorCode;
        Properties = new Dictionary<string, object>();
    }

    /// <summary>
    /// Construtor com exceção interna
    /// </summary>
    public DomainException(string message, Exception innerException) : base(message, innerException)
    {
        ErrorCode = "DOMAIN_ERROR";
        Properties = new Dictionary<string, object>();
    }

    /// <summary>
    /// Construtor completo
    /// </summary>
    public DomainException(
        string message, 
        string errorCode, 
        Dictionary<string, object>? properties = null, 
        Exception? innerException = null) 
        : base(message, innerException)
    {
        ErrorCode = errorCode;
        Properties = properties ?? new Dictionary<string, object>();
    }

    /// <summary>
    /// Adiciona propriedade ao erro
    /// </summary>
    public DomainException WithProperty(string key, object value)
    {
        Properties[key] = value;
        return this;
    }

    /// <summary>
    /// Override do ToString para incluir informações adicionais
    /// </summary>
    public override string ToString()
    {
        var result = $"[{ErrorCode}] {Message}";
        
        if (Properties.Any())
        {
            var props = string.Join(", ", Properties.Select(p => $"{p.Key}={p.Value}"));
            result += $" | Properties: {props}";
        }

        if (InnerException != null)
        {
            result += $" | Inner: {InnerException.Message}";
        }

        return result;
    }
}

/// <summary>
/// Exceção para erros relacionados a engines embarcadas
/// </summary>
public class EngineException : DomainException
{
    /// <summary>
    /// ID da engine relacionada ao erro
    /// </summary>
    public Guid? EngineId { get; }

    /// <summary>
    /// Nome da engine relacionada ao erro
    /// </summary>
    public string? EngineName { get; }

    /// <summary>
    /// Tipo da engine relacionada ao erro
    /// </summary>
    public string? EngineType { get; }

    /// <summary>
    /// Construtor básico
    /// </summary>
    public EngineException(string message) : base(message, "ENGINE_ERROR")
    {
    }

    /// <summary>
    /// Construtor com informações da engine
    /// </summary>
    public EngineException(
        string message, 
        Guid? engineId = null, 
        string? engineName = null, 
        string? engineType = null) 
        : base(message, "ENGINE_ERROR")
    {
        EngineId = engineId;
        EngineName = engineName;
        EngineType = engineType;

        if (engineId.HasValue)
            WithProperty("EngineId", engineId.Value);
        if (!string.IsNullOrWhiteSpace(engineName))
            WithProperty("EngineName", engineName);
        if (!string.IsNullOrWhiteSpace(engineType))
            WithProperty("EngineType", engineType);
    }

    /// <summary>
    /// Construtor com exceção interna
    /// </summary>
    public EngineException(
        string message, 
        Exception innerException, 
        Guid? engineId = null, 
        string? engineName = null, 
        string? engineType = null) 
        : base(message, "ENGINE_ERROR", null, innerException)
    {
        EngineId = engineId;
        EngineName = engineName;
        EngineType = engineType;

        if (engineId.HasValue)
            WithProperty("EngineId", engineId.Value);
        if (!string.IsNullOrWhiteSpace(engineName))
            WithProperty("EngineName", engineName);
        if (!string.IsNullOrWhiteSpace(engineType))
            WithProperty("EngineType", engineType);
    }
}

/// <summary>
/// Exceção para erros relacionados a containers
/// </summary>
public class ContainerException : DomainException
{
    /// <summary>
    /// ID do container relacionado ao erro
    /// </summary>
    public Guid? ContainerId { get; }

    /// <summary>
    /// Nome do container relacionado ao erro
    /// </summary>
    public string? ContainerName { get; }

    /// <summary>
    /// ID do container na engine
    /// </summary>
    public string? EngineContainerId { get; }

    /// <summary>
    /// Construtor básico
    /// </summary>
    public ContainerException(string message) : base(message, "CONTAINER_ERROR")
    {
    }

    /// <summary>
    /// Construtor com informações do container
    /// </summary>
    public ContainerException(
        string message, 
        Guid? containerId = null, 
        string? containerName = null, 
        string? engineContainerId = null) 
        : base(message, "CONTAINER_ERROR")
    {
        ContainerId = containerId;
        ContainerName = containerName;
        EngineContainerId = engineContainerId;

        if (containerId.HasValue)
            WithProperty("ContainerId", containerId.Value);
        if (!string.IsNullOrWhiteSpace(containerName))
            WithProperty("ContainerName", containerName);
        if (!string.IsNullOrWhiteSpace(engineContainerId))
            WithProperty("EngineContainerId", engineContainerId);
    }

    /// <summary>
    /// Construtor com exceção interna
    /// </summary>
    public ContainerException(
        string message, 
        Exception innerException, 
        Guid? containerId = null, 
        string? containerName = null, 
        string? engineContainerId = null) 
        : base(message, "CONTAINER_ERROR", null, innerException)
    {
        ContainerId = containerId;
        ContainerName = containerName;
        EngineContainerId = engineContainerId;

        if (containerId.HasValue)
            WithProperty("ContainerId", containerId.Value);
        if (!string.IsNullOrWhiteSpace(containerName))
            WithProperty("ContainerName", containerName);
        if (!string.IsNullOrWhiteSpace(engineContainerId))
            WithProperty("EngineContainerId", engineContainerId);
    }
}

/// <summary>
/// Exceção para erros de configuração
/// </summary>
public class ConfigurationException : DomainException
{
    /// <summary>
    /// Nome da configuração relacionada ao erro
    /// </summary>
    public string? ConfigurationName { get; }

    /// <summary>
    /// Valor da configuração que causou o erro
    /// </summary>
    public string? ConfigurationValue { get; }

    /// <summary>
    /// Construtor básico
    /// </summary>
    public ConfigurationException(string message) : base(message, "CONFIGURATION_ERROR")
    {
    }

    /// <summary>
    /// Construtor com informações da configuração
    /// </summary>
    public ConfigurationException(
        string message, 
        string? configurationName = null, 
        string? configurationValue = null) 
        : base(message, "CONFIGURATION_ERROR")
    {
        ConfigurationName = configurationName;
        ConfigurationValue = configurationValue;

        if (!string.IsNullOrWhiteSpace(configurationName))
            WithProperty("ConfigurationName", configurationName);
        if (!string.IsNullOrWhiteSpace(configurationValue))
            WithProperty("ConfigurationValue", configurationValue);
    }
}

/// <summary>
/// Exceção para erros de validação
/// </summary>
public class ValidationException : DomainException
{
    /// <summary>
    /// Lista de erros de validação
    /// </summary>
    public List<ValidationError> ValidationErrors { get; }

    /// <summary>
    /// Construtor básico
    /// </summary>
    public ValidationException(string message) : base(message, "VALIDATION_ERROR")
    {
        ValidationErrors = new List<ValidationError>();
    }

    /// <summary>
    /// Construtor com erros de validação
    /// </summary>
    public ValidationException(string message, IEnumerable<ValidationError> validationErrors) 
        : base(message, "VALIDATION_ERROR")
    {
        ValidationErrors = validationErrors.ToList();
        
        WithProperty("ValidationErrorCount", ValidationErrors.Count);
        WithProperty("ValidationErrors", ValidationErrors.Select(e => e.ToString()).ToArray());
    }

    /// <summary>
    /// Construtor com erros do FluentValidation
    /// </summary>
    public ValidationException(string message, IEnumerable<FluentValidation.Results.ValidationFailure> failures) 
        : base(message, "VALIDATION_ERROR")
    {
        ValidationErrors = failures.Select(f => new ValidationError(f.PropertyName, f.ErrorMessage)).ToList();
        
        WithProperty("ValidationErrorCount", ValidationErrors.Count);
        WithProperty("ValidationErrors", ValidationErrors.Select(e => e.ToString()).ToArray());
    }
}

/// <summary>
/// Representa um erro de validação específico
/// </summary>
public record ValidationError(string PropertyName, string ErrorMessage)
{
    public override string ToString() => $"{PropertyName}: {ErrorMessage}";
}
