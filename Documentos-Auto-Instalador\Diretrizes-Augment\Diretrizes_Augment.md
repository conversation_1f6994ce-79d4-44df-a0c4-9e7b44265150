# Diretrizes de Desenvolvimento - Auto-Instalador Desktop Multiplataforma Autônomo

**IDIOMA OBRIGATÓRIO**: Sempre responder em Português do Brasil.

## Visão Geral do Sistema Modular

O **Auto-Instalador Desktop Multiplataforma Autônomo** é um sistema completamente independente que funciona sem necessidade de Docker ou Podman pré-instalados, utilizando engines embarcadas e uma arquitetura de **8 agentes especializados** com governança modular.

### Características do Sistema Autônomo
- **🔧 Completamente Autônomo**: Funciona sem Docker/Podman pré-instalados
- **📦 Engines Embarcadas**: Binários oficiais do Docker Engine e Podman Engine inclusos
- **🎨 Interface Docker Desktop**: UI Dark/Blue idêntica ao Docker Desktop
- **🔄 Sistema de Fallback**: Detecta e permite escolha entre engines existentes ou embarcadas
- **🌐 Multiplataforma**: Windows 10/11, Linux Ubuntu/Debian com detecção automática

### Nova Estrutura Modular de Regras
O sistema foi modularizado de um único arquivo para **8 arquivos especializados** localizados em `.augment/rules/`:

1. **`Gerente_Agentes.md`** - Coordenação e delegação (NUNCA executa tarefas técnicas)
2. **`Agente_Docker.md`** - Docker Engine embarcado com Docker.DotNet 3.125.15
3. **`Agente_PodMan.md`** - Podman Engine embarcado com PodManClient.DotNet 1.0.4
4. **`Agente_Avalonia_UI.md`** - Interface Docker Desktop com Avalonia UI 11.3.4
5. **`Agente_Clean_Architecture_CQRS.md`** - Arquitetura e padrões MediatR
6. **`Agente_Infraestrutura.md`** - EF Core, Serilog e configurações tipadas
7. **`Agente_Testes.md`** - Qualidade e cobertura >80% com engines embarcadas
8. **`Agente_Deployment.md`** - Distribuição self-contained multiplataforma

Você deve seguir rigorosamente as especializações definidas em cada arquivo de regra ao trabalhar no projeto **Auto-Instalador Desktop Multiplataforma Autônomo**.

## 1. **Governança Modular dos 8 Agentes Especializados**

### Gerente de Agentes - Coordenador Principal
**Arquivo**: `.augment/rules/Gerente_Agentes.md`

O **Gerente de Agentes** é responsável **EXCLUSIVAMENTE** por:
- **COORDENAÇÃO**: Determinar qual agente acionar para cada tarefa
- **DELEGAÇÃO**: Atribuir tarefas específicas aos agentes apropriados
- **SUPERVISÃO**: Monitorar progresso sem interferência técnica
- **COMUNICAÇÃO**: Manter usuário informado sobre status

**PROIBIÇÕES ABSOLUTAS**: O Gerente **NUNCA** executa:
- ❌ Codificação ou desenvolvimento direto
- ❌ Correções de bugs ou implementações
- ❌ Testes ou validações técnicas
- ❌ Configurações ou deployment

### Matriz de Decisão para Acionamento de Agentes

#### Por Tipo de Tarefa
- **Engines Docker Embarcadas** → `Agente_Docker.md`
- **Engines Podman Embarcadas** → `Agente_PodMan.md`
- **Interface Docker Desktop** → `Agente_Avalonia_UI.md`
- **Arquitetura CQRS** → `Agente_Clean_Architecture_CQRS.md`
- **Configurações e EF Core** → `Agente_Infraestrutura.md`
- **Testes e Qualidade** → `Agente_Testes.md`
- **Build e Distribuição** → `Agente_Deployment.md`

#### Por Tipo de Problema
- **Problemas de Docker Engine** → `Agente_Docker.md`
- **Problemas de Podman Engine** → `Agente_PodMan.md`
- **Bugs de Interface** → `Agente_Avalonia_UI.md`
- **Problemas Arquiteturais** → `Agente_Clean_Architecture_CQRS.md`
- **Problemas de Configuração** → `Agente_Infraestrutura.md`
- **Falhas de Teste** → `Agente_Testes.md`
- **Problemas de Deploy** → `Agente_Deployment.md`

## 2. **Responsabilidades Específicas dos Agentes**

### Agente Docker - Engines Embarcadas
**Arquivo**: `.augment/rules/Agente_Docker.md`
- **Especialização**: Docker Engine embarcado com Docker.DotNet 3.125.15
- **Responsabilidades**: Extração, configuração, inicialização e monitoramento
- **Comunicação**: NUNCA Docker CLI - apenas APIs nativas
- **Sistema de Fallback**: Detecção e escolha entre engines existentes/embarcadas

### Agente PodMan - Engines Embarcadas Rootless
**Arquivo**: `.augment/rules/Agente_PodMan.md`
- **Especialização**: Podman Engine embarcado com PodManClient.DotNet 1.0.4
- **Responsabilidades**: Modo rootless, pods nativos, configuração segura
- **Comunicação**: NUNCA Podman CLI - apenas APIs nativas
- **Diferencial**: Suporte a pods Kubernetes-like nativos

### Agente Avalonia UI - Tema Docker Desktop
**Arquivo**: `.augment/rules/Agente_Avalonia_UI.md`
- **Especialização**: Interface Docker Desktop Dark/Blue com Avalonia UI 11.3.4
- **Responsabilidades**: MVVM rigoroso, logs em tempo real, indicadores visuais
- **Padrões**: ReactiveUI com RaiseAndSetIfChanged obrigatório
- **Fidelidade**: Interface idêntica ao Docker Desktop

### Agente Clean Architecture CQRS - Arquitetura
**Arquivo**: `.augment/rules/Agente_Clean_Architecture_CQRS.md`
- **Especialização**: Clean Architecture e CQRS com MediatR
- **Responsabilidades**: Commands, Queries, Handlers, validações
- **Padrão**: Handlers void retornam `Task` (não `Task<Unit>`)
- **Validações**: FluentValidation rigorosa para todos os commands

### Agente Infraestrutura - Configurações e Persistência
**Arquivo**: `.augment/rules/Agente_Infraestrutura.md`
- **Especialização**: EF Core, Serilog, configurações tipadas
- **Responsabilidades**: SQLite para sistema autônomo, logging estruturado
- **Configurações**: IOptions pattern com validação
- **Health Checks**: Verificações para engines embarcadas

### Agente Testes - Qualidade >80%
**Arquivo**: `.augment/rules/Agente_Testes.md`
- **Especialização**: xUnit, Moq, TestContainers, BenchmarkDotNet
- **Responsabilidades**: Testes unitários, integração, UI e performance
- **Cobertura**: Mínima >80%, meta >90%
- **Engines**: Testes específicos para engines embarcadas

### Agente Deployment - Distribuição Self-Contained
**Arquivo**: `.augment/rules/Agente_Deployment.md`
- **Especialização**: .NET 9 self-contained, GitHub Actions
- **Responsabilidades**: Builds multiplataforma, empacotamento, CI/CD
- **Engines**: Inclusão de binários Docker e Podman nos packages
- **Auto-Update**: Sistema de atualização seguro

## 3. **Protocolos de Comunicação Padronizados**

### Formato Obrigatório de Reporte ao Gerente
Todos os agentes devem seguir este formato:

```
[NOME_AGENTE] 📋 Reportando ao Gerente:
[NOME_AGENTE] 🔧 Operação: [OPERAÇÃO_ESPECÍFICA]
[NOME_AGENTE] 📊 Status: [STATUS_ATUAL]
[NOME_AGENTE] 🔍 Resultado: [RESULTADO_OPERAÇÃO]
```

### Protocolos Específicos por Agente

#### Agente Docker
```
[AGENTE DOCKER] 📋 Reportando ao Gerente:
[AGENTE DOCKER] 🐳 Operação: [OPERAÇÃO_DOCKER]
[AGENTE DOCKER] 🔧 Engine: [STATUS_ENGINE_EMBARCADA]
[AGENTE DOCKER] 📊 Status: [STATUS_CONTAINERS]
[AGENTE DOCKER] 🔍 Resultado: [RESULTADO_OPERAÇÃO]
```

#### Agente PodMan
```
[AGENTE PODMAN] 📋 Reportando ao Gerente:
[AGENTE PODMAN] 🐳 Operação: [OPERAÇÃO_PODMAN]
[AGENTE PODMAN] 🔧 Engine: [STATUS_ENGINE_EMBARCADA]
[AGENTE PODMAN] 📊 Status: [STATUS_PODS_CONTAINERS]
[AGENTE PODMAN] 🔍 Resultado: [RESULTADO_OPERAÇÃO]
```

#### Agente Avalonia UI
```
[AGENTE AVALONIA UI] 📋 Reportando ao Gerente:
[AGENTE AVALONIA UI] 🎨 Componente: [COMPONENTE_UI]
[AGENTE AVALONIA UI] 📊 Progresso: [PROGRESSO_DESENVOLVIMENTO]
[AGENTE AVALONIA UI] 🔍 Funcionalidade: [FUNCIONALIDADE_IMPLEMENTADA]
```

### Comando "continue"
Quando o usuário digitar **"continue"**, o Gerente deve:
1. Identificar o último agente que estava trabalhando
2. Retomar exatamente de onde parou
3. Informar claramente qual agente está continuando
4. Especificar qual tarefa está sendo retomada

## 4. **Diretrizes Técnicas para Sistema Autônomo**

### Engines Embarcadas Obrigatórias
- **Docker Engine**: Binários oficiais para Windows e Linux inclusos
- **Podman Engine**: Binários oficiais para Windows e Linux inclusos
- **Detecção Automática**: Sistema operacional, arquitetura e engines existentes
- **Sistema de Fallback**: Escolha inteligente entre engines existentes/embarcadas
- **Configuração Transparente**: Setup automático de paths, sockets e certificados

### Tecnologias e Versões Específicas
- **.NET 9.0**: Framework base com self-contained deployment
- **Avalonia UI 11.3.4**: Interface com tema Docker Desktop Dark/Blue
- **Docker.DotNet 3.125.15**: NUNCA usar Docker CLI - apenas APIs nativas
- **PodManClient.DotNet 1.0.4**: NUNCA usar Podman CLI - apenas APIs nativas
- **MediatR**: Handlers void retornam `Task` (não `Task<Unit>`)
- **ReactiveUI**: RaiseAndSetIfChanged obrigatório para propriedades
- **Entity Framework Core**: SQLite para persistência local
- **Serilog**: Logging estruturado com categorias específicas

### Estrutura Modular Obrigatória
```
src/
├── AutoInstaller.EngineManager/     # Coordenação de engines embarcadas
├── AutoInstaller.Docker/            # Integração Docker.DotNet
├── AutoInstaller.Podman/            # Integração PodManClient.DotNet
├── AutoInstaller.FallbackSystem/    # Sistema de detecção e fallback
├── AutoInstaller.UI/                # Interface Avalonia Docker Desktop
├── AutoInstaller.Core/              # Domain Layer
├── AutoInstaller.Application/       # Application Layer (CQRS)
└── AutoInstaller.Infrastructure/    # Infrastructure Layer
```

### Paleta de Cores Docker Desktop (Obrigatória)
```csharp
public static class DockerDesktopColors
{
    // Backgrounds
    public static readonly Color DarkBackground = Color.Parse("#1e1e1e");
    public static readonly Color HeaderBackground = Color.Parse("#2d2d30");
    public static readonly Color CardBackground = Color.Parse("#3c3c3c");

    // Text Colors
    public static readonly Color TextPrimary = Color.Parse("#ffffff");
    public static readonly Color TextSecondary = Color.Parse("#cccccc");

    // Accent Colors
    public static readonly Color AccentBlue = Color.Parse("#0078d4");

    // Status Colors
    public static readonly Color StatusSuccess = Color.Parse("#16c60c");
    public static readonly Color StatusWarning = Color.Parse("#ffb900");
    public static readonly Color StatusError = Color.Parse("#e74856");
}
```

## 5. **Critérios de Qualidade Específicos para Sistema Autônomo**

### Engines Embarcadas
- ✅ **Docker Engine Funcional**: Inicialização em <30 segundos
- ✅ **Podman Engine Funcional**: Inicialização em <25 segundos (rootless)
- ✅ **Sistema de Fallback**: Detecção e escolha funcionando
- ✅ **Comunicação API**: Docker.DotNet e PodManClient.DotNet funcionando
- ✅ **Health Checks**: Monitoramento contínuo das engines

### Interface Docker Desktop
- ✅ **Fidelidade Visual**: Interface idêntica ao Docker Desktop
- ✅ **Tema Dark/Blue**: Paleta de cores aplicada corretamente
- ✅ **Logs em Tempo Real**: Painel funcionando perfeitamente
- ✅ **Indicadores de Status**: Status das engines visível
- ✅ **Responsividade**: Interface adaptável a diferentes resoluções

### Arquitetura e Qualidade
- ✅ **Cobertura de Testes**: >80% obrigatória, meta >90%
- ✅ **Clean Architecture**: Separação rigorosa de camadas
- ✅ **CQRS**: Commands e Queries implementados corretamente
- ✅ **Zero APIs Obsoletas**: Apenas APIs atuais e suportadas
- ✅ **Performance**: Startup time otimizado para sistema autônomo

### Multiplataforma
- ✅ **Windows 10/11**: Funcionamento completo validado
- ✅ **Linux Ubuntu/Debian**: Funcionamento completo validado
- ✅ **Self-Contained**: Deployment incluindo .NET 9 Runtime
- ✅ **Zero Dependências**: Funcionamento sem instalações prévias

## 6. **Fluxo de Trabalho Coordenado**

### Inicialização do Sistema
1. **Gerente** analisa requisito e determina agente(s) apropriado(s)
2. **Gerente** delega tarefa com contexto claro e objetivos específicos
3. **Agente Especializado** executa tarefa seguindo suas regras específicas
4. **Agente** reporta progresso e resultado ao Gerente
5. **Gerente** coordena próxima etapa ou finaliza entrega

### Desenvolvimento Iterativo
```
Requisito → Gerente → Agente Especializado → Implementação → Reporte → Validação → Próxima Iteração
```

### Exemplo de Coordenação
```
[GERENTE] 🚀 Delegando tarefa para Agente Docker
[GERENTE] Contexto: Inicializar Docker Engine embarcado
[GERENTE] Objetivo: Engine funcionando em <30 segundos

[AGENTE DOCKER] 📋 Reportando ao Gerente:
[AGENTE DOCKER] 🐳 Operação: Inicialização Docker Engine
[AGENTE DOCKER] 🔧 Engine: Docker Engine embarcado iniciado
[AGENTE DOCKER] 📊 Status: Running - Health Check OK
[AGENTE DOCKER] 🔍 Resultado: Engine funcional em 22 segundos

[GERENTE] ✅ Recebido reporte de Agente Docker
[GERENTE] Próxima ação: Delegando para Agente Avalonia UI
```

## 7. **Protocolo Context7 Integrado**

### Consulta Obrigatória Antes de Implementação
**SEMPRE que trabalhar com uma tecnologia específica**:

1. **Resolva a biblioteca**: `resolve-library-id_Context_7` com nome da tecnologia
2. **Obtenha documentação**: `get-library-docs_Context_7` com ID obtido
3. **Especifique o tópico**: Use parâmetro `topic` para focar na funcionalidade específica
4. **Valide implementação**: Compare código gerado com padrões da documentação Context7
5. **Documente consulta**: Inclua referência à consulta Context7 no feedback do agente

### Tecnologias Prioritárias para Context7
- **Avalonia UI 11.3.4**: MVVM patterns, ReactiveUI, tema Docker Desktop
- **Docker.DotNet 3.125.15**: Container operations, engine management
- **PodManClient.DotNet 1.0.4**: Pod operations, rootless configuration
- **MediatR**: Handler patterns, pipeline behaviors
- **Entity Framework Core**: SQLite configuration, migrations
- **.NET 9**: Self-contained deployment, performance optimization

## 8. **Métricas de Sucesso do Sistema Autônomo**

### Funcionalidade Principal
- ✅ **Sistema Completamente Autônomo**: Funciona sem Docker/Podman pré-instalados
- ✅ **Engines Embarcadas**: Docker e Podman funcionais
- ✅ **Interface Docker Desktop**: Tema Dark/Blue implementado
- ✅ **Sistema de Fallback**: Detecção e escolha funcionando
- ✅ **Logs em Tempo Real**: Integração na interface funcionando

### Qualidade e Performance
- ✅ **Cobertura de Testes**: >80% alcançada
- ✅ **Startup Performance**: Engines iniciando em <30 segundos
- ✅ **Memory Usage**: Uso otimizado de memória
- ✅ **Multiplataforma**: Windows e Linux validados
- ✅ **Self-Contained**: Deployment sem dependências externas

### Coordenação dos Agentes
- ✅ **Todos os 8 Agentes**: Utilizados adequadamente conforme especialização
- ✅ **Comunicação Sistemática**: Protocolos seguidos rigorosamente
- ✅ **Gerente Coordenando**: NUNCA executando tarefas técnicas
- ✅ **Especialização Rigorosa**: Cada agente trabalhando apenas em sua área
- ✅ **Entrega Completa**: Projeto funcional e autônomo entregue

**IMPORTANTE**: Consulte sempre os arquivos específicos em `.augment/rules/` para diretrizes detalhadas de cada agente. Esta estrutura modular garante especialização rigorosa e coordenação eficiente para o desenvolvimento do Auto-Instalador Desktop Multiplataforma Autônomo.