namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value Object para limites de recursos de containers
/// </summary>
public class ResourceLimits : ValueObject
{
    /// <summary>
    /// Limite de memória em bytes (null = sem limite)
    /// </summary>
    public long? MemoryBytes { get; }

    /// <summary>
    /// Limite de memória em formato legível (ex: "512m", "1g")
    /// </summary>
    public string? Memory { get; }

    /// <summary>
    /// Limite de CPU em cores (ex: 0.5, 1.0, 2.0)
    /// </summary>
    public double? CpuCores { get; }

    /// <summary>
    /// Limite de CPU em formato de quota (ex: "50000" para 0.5 cores)
    /// </summary>
    public long? CpuQuota { get; }

    /// <summary>
    /// Período de CPU em microssegundos (padrão: 100000)
    /// </summary>
    public long CpuPeriod { get; }

    /// <summary>
    /// Peso de CPU (1-1024, padrão: 1024)
    /// </summary>
    public int CpuWeight { get; }

    /// <summary>
    /// Limite de swap em bytes (null = sem limite)
    /// </summary>
    public long? SwapBytes { get; }

    /// <summary>
    /// Limite de PIDs (null = sem limite)
    /// </summary>
    public long? PidsLimit { get; }

    /// <summary>
    /// Limite de operações de I/O por segundo (null = sem limite)
    /// </summary>
    public long? IoOpsPerSecond { get; }

    /// <summary>
    /// Limite de bytes de I/O por segundo (null = sem limite)
    /// </summary>
    public long? IoBytesPerSecond { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    public ResourceLimits(
        long? memoryBytes = null,
        string? memory = null,
        double? cpuCores = null,
        long? cpuQuota = null,
        long cpuPeriod = 100000,
        int cpuWeight = 1024,
        long? swapBytes = null,
        long? pidsLimit = null,
        long? ioOpsPerSecond = null,
        long? ioBytesPerSecond = null)
    {
        MemoryBytes = memoryBytes;
        Memory = memory;
        CpuCores = cpuCores;
        CpuQuota = cpuQuota;
        CpuPeriod = cpuPeriod > 0 ? cpuPeriod : 100000;
        CpuWeight = cpuWeight is >= 1 and <= 1024 ? cpuWeight : 1024;
        SwapBytes = swapBytes;
        PidsLimit = pidsLimit;
        IoOpsPerSecond = ioOpsPerSecond;
        IoBytesPerSecond = ioBytesPerSecond;
    }

    /// <summary>
    /// Cria limites padrão para desenvolvimento
    /// </summary>
    public static ResourceLimits CreateDefault()
    {
        return new ResourceLimits(
            memory: "512m",
            cpuCores: 1.0,
            pidsLimit: 1024
        );
    }

    /// <summary>
    /// Cria limites para ambiente de produção
    /// </summary>
    public static ResourceLimits CreateProduction()
    {
        return new ResourceLimits(
            memory: "2g",
            cpuCores: 2.0,
            pidsLimit: 4096,
            ioOpsPerSecond: 1000,
            ioBytesPerSecond: 100 * 1024 * 1024 // 100MB/s
        );
    }

    /// <summary>
    /// Cria limites mínimos para containers leves
    /// </summary>
    public static ResourceLimits CreateMinimal()
    {
        return new ResourceLimits(
            memory: "128m",
            cpuCores: 0.25,
            pidsLimit: 256
        );
    }

    /// <summary>
    /// Converte memória em formato legível para bytes
    /// </summary>
    public long? GetMemoryInBytes()
    {
        if (MemoryBytes.HasValue)
            return MemoryBytes.Value;

        if (string.IsNullOrWhiteSpace(Memory))
            return null;

        return ParseMemoryString(Memory);
    }

    /// <summary>
    /// Converte CPU cores para quota
    /// </summary>
    public long? GetCpuQuota()
    {
        if (CpuQuota.HasValue)
            return CpuQuota.Value;

        if (CpuCores.HasValue)
            return (long)(CpuCores.Value * CpuPeriod);

        return null;
    }

    /// <summary>
    /// Verifica se há limites definidos
    /// </summary>
    public bool HasLimits()
    {
        return MemoryBytes.HasValue ||
               !string.IsNullOrWhiteSpace(Memory) ||
               CpuCores.HasValue ||
               CpuQuota.HasValue ||
               SwapBytes.HasValue ||
               PidsLimit.HasValue ||
               IoOpsPerSecond.HasValue ||
               IoBytesPerSecond.HasValue;
    }

    /// <summary>
    /// Valida se os limites são consistentes
    /// </summary>
    public bool IsValid()
    {
        // Memória não pode ser negativa
        if (MemoryBytes.HasValue && MemoryBytes.Value < 0)
            return false;

        // CPU cores deve ser positivo
        if (CpuCores.HasValue && CpuCores.Value <= 0)
            return false;

        // CPU quota deve ser positiva
        if (CpuQuota.HasValue && CpuQuota.Value <= 0)
            return false;

        // PIDs limit deve ser positivo
        if (PidsLimit.HasValue && PidsLimit.Value <= 0)
            return false;

        // IO limits devem ser positivos
        if (IoOpsPerSecond.HasValue && IoOpsPerSecond.Value <= 0)
            return false;

        if (IoBytesPerSecond.HasValue && IoBytesPerSecond.Value <= 0)
            return false;

        return true;
    }

    /// <summary>
    /// Parse de string de memória para bytes
    /// </summary>
    private static long ParseMemoryString(string memory)
    {
        if (string.IsNullOrWhiteSpace(memory))
            return 0;

        memory = memory.Trim().ToLowerInvariant();

        var multiplier = 1L;
        var numberPart = memory;

        if (memory.EndsWith("k") || memory.EndsWith("kb"))
        {
            multiplier = 1024L;
            numberPart = memory.TrimEnd('k', 'b');
        }
        else if (memory.EndsWith("m") || memory.EndsWith("mb"))
        {
            multiplier = 1024L * 1024L;
            numberPart = memory.TrimEnd('m', 'b');
        }
        else if (memory.EndsWith("g") || memory.EndsWith("gb"))
        {
            multiplier = 1024L * 1024L * 1024L;
            numberPart = memory.TrimEnd('g', 'b');
        }
        else if (memory.EndsWith("t") || memory.EndsWith("tb"))
        {
            multiplier = 1024L * 1024L * 1024L * 1024L;
            numberPart = memory.TrimEnd('t', 'b');
        }

        if (long.TryParse(numberPart, out var number))
        {
            return number * multiplier;
        }

        if (double.TryParse(numberPart, out var doubleNumber))
        {
            return (long)(doubleNumber * multiplier);
        }

        throw new ArgumentException($"Formato de memória inválido: {memory}");
    }

    /// <summary>
    /// Implementação de igualdade para Value Object
    /// </summary>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return MemoryBytes ?? 0L;
        yield return Memory ?? string.Empty;
        yield return CpuCores ?? 0.0;
        yield return CpuQuota ?? 0L;
        yield return CpuPeriod;
        yield return CpuWeight;
        yield return SwapBytes ?? 0L;
        yield return PidsLimit ?? 0L;
        yield return IoOpsPerSecond ?? 0L;
        yield return IoBytesPerSecond ?? 0L;
    }
}
