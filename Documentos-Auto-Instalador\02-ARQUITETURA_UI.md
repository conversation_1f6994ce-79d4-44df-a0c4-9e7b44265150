# Arquitetura UI - Auto-Instalador Desktop Multiplataforma Autônomo

## Visão Geral
Este documento apresenta a arquitetura da interface do usuário para o **Auto-Instalador Desktop Multiplataforma Autônomo**, seguindo rigorosamente o padrão visual do Docker Desktop com tema Dark/Blue, incluindo logs em tempo real das engines embarcadas e controles avançados para gerenciamento de Docker e Podman.

---

## 1. Arquitetura UI para Sistema Autônomo

### 1.1 Estrutura MVVM com Tema Docker Desktop
```
src/AutoInstaller.UI/
├── ViewModels/           # Lógica de apresentação reativa
│   ├── MainWindowViewModel.cs        # Janela principal com status de engines
│   ├── DashboardViewModel.cs         # Dashboard estilo Docker Desktop
│   ├── EngineManagerViewModel.cs     # Gerenciamento de engines embarcadas
│   ├── LogsViewModel.cs              # Logs em tempo real integrados
│   ├── SettingsViewModel.cs          # Configurações avançadas
│   └── FallbackViewModel.cs          # Seleção de engines (existente/embarcada)
├── Views/               # Interfaces visuais Dark/Blue
│   ├── MainWindow.axaml              # Layout principal Docker Desktop
│   ├── DashboardView.axaml           # Cards arredondados e indicadores
│   ├── EngineManagerView.axaml       # Controles de engines embarcadas
│   ├── LogsView.axaml                # Painel de logs integrado
│   ├── SettingsView.axaml            # Configurações com seções expansíveis
│   └── FallbackView.axaml            # Modal de seleção de engine
├── Models/              # Modelos de dados da UI
│   ├── EngineStatus.cs               # Status das engines embarcadas
│   ├── LogEntry.cs                   # Entradas de log estruturadas
│   └── ContainerInfo.cs              # Informações de containers
├── Services/            # Serviços específicos da UI
│   ├── ThemeService.cs               # Gerenciamento do tema Docker Desktop
│   ├── LoggingUIService.cs           # Integração de logs na interface
│   └── EngineUIService.cs            # Interface para engines embarcadas
├── Converters/          # Conversores de dados
│   ├── StatusToColorConverter.cs     # Cores para status (verde/vermelho/amarelo)
│   ├── LogLevelToIconConverter.cs    # Ícones para níveis de log
│   └── EngineTypeToIconConverter.cs  # Ícones Docker/Podman
└── Styles/              # Recursos visuais Docker Desktop
    ├── DockerDesktopTheme.axaml      # Tema principal Dark/Blue
    ├── Colors.axaml                  # Paleta de cores Docker Desktop
    ├── Cards.axaml                   # Estilos de cards arredondados
    ├── Buttons.axaml                 # Botões com bordas suaves
    └── Animations.axaml              # Transições de 200-300ms
```

### 1.2 Padrões Implementados para Sistema Autônomo
- **ViewLocator**: Localização automática de Views baseada em ViewModels
- **ReactiveUI**: Propriedades reativas com RaiseAndSetIfChanged
- **Dependency Injection**: Injeção via Microsoft.Extensions.DI
- **Command Pattern**: Comandos reativos para ações de engines
- **Observer Pattern**: Observação de status de engines embarcadas
- **Strategy Pattern**: Seleção entre engines Docker/Podman
- **Factory Pattern**: Criação de clientes Docker.DotNet/PodManClient.DotNet

---

## 2. Diretrizes para Recriação das Telas Principais

### 2.1 MainWindow - Estilo Docker Desktop Autônomo

#### 2.1.1 Estrutura Layout com Tema Dark/Blue
```xml
<Window x:Class="AutoInstaller.UI.Views.MainWindow"
        Title="Auto-Instalador Desktop Multiplataforma Autônomo"
        Width="1400" Height="900"
        MinWidth="1200" MinHeight="700"
        WindowStartupLocation="CenterScreen"
        Icon="/Assets/docker-style-icon.ico"
        Background="{DynamicResource DockerDarkBackground}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60" />        <!-- Header com status de engines -->
            <RowDefinition Height="*" />         <!-- Content Area principal -->
            <RowDefinition Height="200" />       <!-- Painel de logs integrado -->
            <RowDefinition Height="30" />        <!-- Status Bar com indicadores -->
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="280" />    <!-- Sidebar com navegação -->
            <ColumnDefinition Width="*" />      <!-- Main Content área -->
            <ColumnDefinition Width="300" />    <!-- Painel lateral de controles -->
        </Grid.ColumnDefinitions>

        <!-- Header com Status de Engines Embarcadas -->
        <Border Grid.Row="0" Grid.ColumnSpan="3"
                Background="{DynamicResource DockerHeaderBackground}">
            <Grid>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="20,0">
                    <Image Source="/Assets/logo-docker-style.png" Width="32" Height="32"/>
                    <TextBlock Text="Auto-Instalador Autônomo"
                               FontSize="18" FontWeight="SemiBold"
                               Foreground="{DynamicResource DockerTextPrimary}"/>
                </StackPanel>

                <!-- Indicadores de Status das Engines -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Border Background="{DynamicResource DockerCardBackground}"
                            CornerRadius="8" Padding="12,6" Margin="5,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="8" Height="8"
                                     Fill="{Binding DockerEngineStatus, Converter={StaticResource StatusToColorConverter}}"/>
                            <TextBlock Text="Docker Engine" FontSize="12"/>
                        </StackPanel>
                    </Border>

                    <Border Background="{DynamicResource DockerCardBackground}"
                            CornerRadius="8" Padding="12,6" Margin="5,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="8" Height="8"
                                     Fill="{Binding PodmanEngineStatus, Converter={StaticResource StatusToColorConverter}}"/>
                            <TextBlock Text="Podman Engine" FontSize="12"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Sidebar, Main Content e Logs serão detalhados em seções específicas -->
    </Grid>
</Window>
```

#### 2.1.2 Componentes Principais
1. **Header/Toolbar** (Grid.Row="0", Grid.ColumnSpan="2")
   - Logo e título da aplicação
   - Controles de janela (minimizar, maximizar, fechar)
   - Indicador de status de conexão Docker/Podman

2. **Sidebar Navigation** (Grid.Row="1", Grid.Column="0")
   - Menu principal com ícones
   - Indicadores de status
   - Botão de configurações

3. **Main Content Area** (Grid.Row="1", Grid.Column="1")
   - ContentControl para navegação entre views
   - Transições animadas entre telas

4. **Status Bar** (Grid.Row="2", Grid.ColumnSpan="2")
   - Informações de sistema
   - Progresso de operações
   - Logs resumidos

### 2.2 Dashboard (Tela Principal)

#### 2.2.1 Layout em Cards
```xml
<UserControl x:Class="AutoInstaller.UI.Views.DashboardView">
    <ScrollViewer>
        <StackPanel Margin="20" Spacing="20">
            
            <!-- Header Section -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                
                <TextBlock Text="Dashboard" 
                          FontSize="24" 
                          FontWeight="Bold" />
                          
                <Button Grid.Column="1" 
                       Content="Novo Container" 
                       Classes="primary" />
            </Grid>
            
            <!-- Quick Stats Cards -->
            <UniformGrid Columns="4" Spacing="15">
                <Border Classes="stat-card">
                    <!-- Containers Ativos -->
                </Border>
                <Border Classes="stat-card">
                    <!-- Imagens Locais -->
                </Border>
                <Border Classes="stat-card">
                    <!-- Uso de CPU -->
                </Border>
                <Border Classes="stat-card">
                    <!-- Uso de Memória -->
                </Border>
            </UniformGrid>
            
            <!-- Containers List -->
            <Border Classes="content-card">
                <DataGrid ItemsSource="{Binding Containers}"
                         AutoGenerateColumns="False">
                    <!-- Colunas definidas -->
                </DataGrid>
            </Border>
            
        </StackPanel>
    </ScrollViewer>
</UserControl>
```

#### 2.2.2 Funcionalidades
- **Cards de Estatísticas**: Métricas em tempo real
- **Lista de Containers**: DataGrid com ações contextuais
- **Filtros e Busca**: Barra de ferramentas para filtragem
- **Ações Rápidas**: Botões para operações comuns

### 2.3 Container Management (Gerenciamento)

#### 2.3.1 Estrutura de Abas
```xml
<TabView ItemsSource="{Binding TabItems}" 
         SelectedItem="{Binding SelectedTab}">
    
    <TabView.ItemTemplate>
        <DataTemplate>
            <TabViewItem Header="{Binding Title}" 
                        IsClosable="{Binding IsClosable}">
                <ContentControl Content="{Binding Content}" />
            </TabViewItem>
        </DataTemplate>
    </TabView.ItemTemplate>
    
</TabView>
```

#### 2.3.2 Abas Disponíveis
1. **Containers**: Lista e gerenciamento de containers
2. **Images**: Gerenciamento de imagens Docker
3. **Networks**: Configuração de redes
4. **Volumes**: Gerenciamento de volumes
5. **Compose**: Projetos Docker Compose

### 2.4 Settings (Configurações)

#### 2.4.1 Layout em Seções
```xml
<UserControl x:Class="AutoInstaller.UI.Views.SettingsView">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="200" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        
        <!-- Settings Navigation -->
        <ListBox Grid.Column="0" 
                ItemsSource="{Binding SettingsCategories}"
                SelectedItem="{Binding SelectedCategory}" />
        
        <!-- Settings Content -->
        <ContentControl Grid.Column="1" 
                       Content="{Binding SelectedCategory.Content}" 
                       Margin="20" />
    </Grid>
</UserControl>
```

#### 2.4.2 Categorias de Configuração
1. **Geral**: Configurações básicas da aplicação
2. **Docker**: Configurações de conexão Docker
3. **Podman**: Configurações de conexão Podman
4. **Interface**: Personalização da UI
5. **Segurança**: Configurações de segurança
6. **Avançado**: Configurações técnicas

---

## 3. Sistema de Modais

### 3.1 Modal Base (Componente Reutilizável)

```xml
<UserControl x:Class="AutoInstaller.UI.Controls.ModalBase">
    <Border Background="{DynamicResource ModalOverlayBrush}"
            IsVisible="{Binding IsVisible}">
        
        <Border Classes="modal-container"
               HorizontalAlignment="Center"
               VerticalAlignment="Center">
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                
                <!-- Header -->
                <Border Grid.Row="0" Classes="modal-header">
                    <Grid>
                        <TextBlock Text="{Binding Title}" 
                                  FontWeight="Bold" 
                                  FontSize="16" />
                        <Button Classes="close-button" 
                               Command="{Binding CloseCommand}" 
                               HorizontalAlignment="Right" />
                    </Grid>
                </Border>
                
                <!-- Content -->
                <ContentControl Grid.Row="1" 
                               Content="{Binding Content}" 
                               Classes="modal-content" />
                
                <!-- Footer -->
                <Border Grid.Row="2" Classes="modal-footer">
                    <StackPanel Orientation="Horizontal" 
                               HorizontalAlignment="Right" 
                               Spacing="10">
                        <Button Content="Cancelar" 
                               Command="{Binding CancelCommand}" 
                               Classes="secondary" />
                        <Button Content="{Binding PrimaryButtonText}" 
                               Command="{Binding PrimaryCommand}" 
                               Classes="primary" />
                    </StackPanel>
                </Border>
                
            </Grid>
        </Border>
    </Border>
</UserControl>
```

### 3.2 Modais Específicos

#### 3.2.1 CreateContainerModal
- **Propósito**: Criação de novos containers
- **Seções**: Configuração básica, rede, volumes, variáveis
- **Validação**: Formulário com validação em tempo real

#### 3.2.2 ImagePullModal
- **Propósito**: Download de imagens Docker
- **Funcionalidades**: Busca em registries, progresso de download
- **Feedback**: Barra de progresso e logs em tempo real

#### 3.2.3 ConfirmationModal
- **Propósito**: Confirmação de ações destrutivas
- **Variações**: Delete, Stop, Remove
- **Segurança**: Confirmação dupla para ações críticas

---

## 4. Sistema de Navegação

### 4.1 Navegação Principal (Sidebar)

```csharp
public class NavigationItem
{
    public string Title { get; set; }
    public string Icon { get; set; }
    public Type ViewModelType { get; set; }
    public bool IsSelected { get; set; }
    public bool HasNotification { get; set; }
    public int NotificationCount { get; set; }
}

public class NavigationService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ObservableCollection<NavigationItem> _navigationItems;
    
    public void NavigateTo<TViewModel>() where TViewModel : ViewModelBase
    {
        var viewModel = _serviceProvider.GetRequiredService<TViewModel>();
        CurrentViewModel = viewModel;
        
        // Atualizar seleção na sidebar
        UpdateSelection(typeof(TViewModel));
        
        // Trigger de animação de transição
        TriggerTransition();
    }
}
```

### 4.2 Itens de Navegação

```csharp
public static class NavigationItems
{
    public static readonly NavigationItem[] Items = 
    {
        new() { Title = "Dashboard", Icon = "Home", ViewModelType = typeof(DashboardViewModel) },
        new() { Title = "Containers", Icon = "Container", ViewModelType = typeof(ContainersViewModel) },
        new() { Title = "Images", Icon = "Image", ViewModelType = typeof(ImagesViewModel) },
        new() { Title = "Networks", Icon = "Network", ViewModelType = typeof(NetworksViewModel) },
        new() { Title = "Volumes", Icon = "Storage", ViewModelType = typeof(VolumesViewModel) },
        new() { Title = "Compose", Icon = "Stack", ViewModelType = typeof(ComposeViewModel) },
        new() { Title = "Settings", Icon = "Settings", ViewModelType = typeof(SettingsViewModel) }
    };
}
```

### 4.3 Breadcrumb Navigation

```xml
<ItemsControl ItemsSource="{Binding BreadcrumbItems}">
    <ItemsControl.ItemsPanel>
        <ItemsPanelTemplate>
            <StackPanel Orientation="Horizontal" Spacing="5" />
        </ItemsPanelTemplate>
    </ItemsControl.ItemsPanel>
    
    <ItemsControl.ItemTemplate>
        <DataTemplate>
            <StackPanel Orientation="Horizontal" Spacing="5">
                <Button Content="{Binding Title}" 
                       Command="{Binding NavigateCommand}"
                       Classes="breadcrumb-item" />
                <TextBlock Text="/" 
                          IsVisible="{Binding !IsLast}" 
                          Classes="breadcrumb-separator" />
            </StackPanel>
        </DataTemplate>
    </ItemsControl.ItemTemplate>
</ItemsControl>
```

---

## 5. Tema Docker Desktop Dark

### 5.1 Paleta de Cores

```xml
<ResourceDictionary>
    <!-- Background Colors -->
    <Color x:Key="PrimaryBackgroundColor">#FF1E1E1E</Color>
    <Color x:Key="SecondaryBackgroundColor">#FF2D2D30</Color>
    <Color x:Key="TertiaryBackgroundColor">#FF3E3E42</Color>
    
    <!-- Text Colors -->
    <Color x:Key="PrimaryTextColor">#FFFFFFFF</Color>
    <Color x:Key="SecondaryTextColor">#FFB0B0B0</Color>
    <Color x:Key="DisabledTextColor">#FF6D6D6D</Color>
    
    <!-- Accent Colors -->
    <Color x:Key="AccentColor">#FF0078D4</Color>
    <Color x:Key="AccentHoverColor">#FF106EBE</Color>
    <Color x:Key="AccentPressedColor">#FF005A9E</Color>
    
    <!-- Status Colors -->
    <Color x:Key="SuccessColor">#FF107C10</Color>
    <Color x:Key="WarningColor">#FFFF8C00</Color>
    <Color x:Key="ErrorColor">#FFD13438</Color>
    <Color x:Key="InfoColor">#FF0078D4</Color>
    
    <!-- Border Colors -->
    <Color x:Key="BorderColor">#FF484848</Color>
    <Color x:Key="FocusBorderColor">#FF0078D4</Color>
</ResourceDictionary>
```

### 5.2 Tipografia

```xml
<ResourceDictionary>
    <!-- Font Families -->
    <FontFamily x:Key="PrimaryFontFamily">Inter, Segoe UI, sans-serif</FontFamily>
    <FontFamily x:Key="MonospaceFontFamily">Consolas, Courier New, monospace</FontFamily>
    
    <!-- Font Sizes -->
    <x:Double x:Key="FontSizeSmall">12</x:Double>
    <x:Double x:Key="FontSizeNormal">14</x:Double>
    <x:Double x:Key="FontSizeMedium">16</x:Double>
    <x:Double x:Key="FontSizeLarge">18</x:Double>
    <x:Double x:Key="FontSizeXLarge">24</x:Double>
    
    <!-- Font Weights -->
    <FontWeight x:Key="FontWeightLight">Light</FontWeight>
    <FontWeight x:Key="FontWeightNormal">Normal</FontWeight>
    <FontWeight x:Key="FontWeightMedium">Medium</FontWeight>
    <FontWeight x:Key="FontWeightBold">Bold</FontWeight>
</ResourceDictionary>
```

### 5.3 Estilos de Controles

#### 5.3.1 Button Styles
```xml
<Style Selector="Button.primary">
    <Setter Property="Background" Value="{DynamicResource AccentBrush}" />
    <Setter Property="Foreground" Value="White" />
    <Setter Property="BorderThickness" Value="0" />
    <Setter Property="Padding" Value="16,8" />
    <Setter Property="CornerRadius" Value="4" />
    <Setter Property="FontWeight" Value="Medium" />
</Style>

<Style Selector="Button.secondary">
    <Setter Property="Background" Value="Transparent" />
    <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}" />
    <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}" />
    <Setter Property="BorderThickness" Value="1" />
    <Setter Property="Padding" Value="16,8" />
    <Setter Property="CornerRadius" Value="4" />
</Style>
```

#### 5.3.2 Card Styles
```xml
<Style Selector="Border.content-card">
    <Setter Property="Background" Value="{DynamicResource SecondaryBackgroundBrush}" />
    <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}" />
    <Setter Property="BorderThickness" Value="1" />
    <Setter Property="CornerRadius" Value="8" />
    <Setter Property="Padding" Value="20" />
    <Setter Property="BoxShadow" Value="0 2 8 rgba(0,0,0,0.1)" />
</Style>

<Style Selector="Border.stat-card">
    <Setter Property="Background" Value="{DynamicResource TertiaryBackgroundBrush}" />
    <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}" />
    <Setter Property="BorderThickness" Value="1" />
    <Setter Property="CornerRadius" Value="6" />
    <Setter Property="Padding" Value="16" />
    <Setter Property="MinHeight" Value="100" />
</Style>
```

---

## 6. Transições e Animações

### 6.1 Transições de Página

```xml
<Style Selector="ContentControl.page-transition">
    <Setter Property="PageTransition">
        <Setter.Value>
            <CompositePageTransition>
                <PageSlide Duration="0:0:0.3" Orientation="Horizontal" />
            </CompositePageTransition>
        </Setter.Value>
    </Setter>
</Style>
```

### 6.2 Animações de Hover

```xml
<Style Selector="Button:pointerover">
    <Style.Animations>
        <Animation Duration="0:0:0.2">
            <KeyFrame Cue="100%">
                <Setter Property="Opacity" Value="0.8" />
                <Setter Property="(TransformOperations.Transform)" 
                       Value="scale(1.02)" />
            </KeyFrame>
        </Animation>
    </Style.Animations>
</Style>
```

### 6.3 Loading Animations

```xml
<Style Selector="Border.loading-spinner">
    <Style.Animations>
        <Animation Duration="0:0:1" IterationCount="Infinite">
            <KeyFrame Cue="100%">
                <Setter Property="(RotateTransform.Angle)" Value="360" />
            </KeyFrame>
        </Animation>
    </Style.Animations>
</Style>
```

---

## 7. Responsividade e Adaptação

### 7.1 Breakpoints

```csharp
public static class Breakpoints
{
    public const double Small = 768;
    public const double Medium = 1024;
    public const double Large = 1440;
    public const double XLarge = 1920;
}
```

### 7.2 Layout Adaptativo

```xml
<Style Selector="UniformGrid.responsive">
    <Setter Property="Columns" Value="4" />
</Style>

<Style Selector="UniformGrid.responsive[Width<1024]">
    <Setter Property="Columns" Value="2" />
</Style>

<Style Selector="UniformGrid.responsive[Width<768]">
    <Setter Property="Columns" Value="1" />
</Style>
```

---

## 8. Acessibilidade

### 8.1 Suporte a Teclado
- **Tab Navigation**: Ordem lógica de navegação
- **Keyboard Shortcuts**: Atalhos para ações principais
- **Focus Indicators**: Indicadores visuais claros

### 8.2 Suporte a Screen Readers
- **AutomationProperties**: Propriedades de automação
- **Semantic Markup**: Uso correto de controles semânticos
- **Alt Text**: Textos alternativos para imagens

### 8.3 Contraste e Legibilidade
- **High Contrast**: Suporte a modo de alto contraste
- **Font Scaling**: Suporte a escalabilidade de fontes
- **Color Blind**: Não dependência exclusiva de cores

---

*Documento gerado em: Janeiro 2025*
*Versão: 1.0*
*Autor: Arquiteto de Software - Auto-Instalador*