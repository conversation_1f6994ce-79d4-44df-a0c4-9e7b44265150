<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <RootNamespace>AutoInstaller.UI</RootNamespace>
    <AssemblyName>AutoInstaller</AssemblyName>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
    <LangVersion>latest</LangVersion>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <UseWPF>false</UseWPF>
    <UseWindowsForms>false</UseWindowsForms>
    <ApplicationIcon>Assets\app-icon.ico</ApplicationIcon>
    <AssemblyTitle>Auto-Instalador Desktop Multiplataforma Autônomo</AssemblyTitle>
    <AssemblyDescription>Sistema autônomo para gerenciamento de containers com Docker e Podman embarcados</AssemblyDescription>
    <AssemblyCompany>DRS Developer</AssemblyCompany>
    <AssemblyProduct>Auto-Instalador Max</AssemblyProduct>
    <Copyright>© 2024 DRS Developer. Todos os direitos reservados.</Copyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <PublishReadyToRun>true</PublishReadyToRun>
    <PublishTrimmed>false</PublishTrimmed>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.3.4" />
    <PackageReference Include="Avalonia.Desktop" Version="11.3.4" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.4" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.4" />
    <PackageReference Include="Avalonia.ReactiveUI" Version="11.3.4" />
    <PackageReference Include="ReactiveUI" Version="20.1.63" />
    <PackageReference Include="ReactiveUI.Fody" Version="19.5.41" />
    <PackageReference Include="Material.Icons.Avalonia" Version="2.1.10" />
    <PackageReference Include="Avalonia.Svg.Skia" Version="11.3.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AutoInstaller.Core\AutoInstaller.Core.csproj" />
    <ProjectReference Include="..\AutoInstaller.Application\AutoInstaller.Application.csproj" />
    <ProjectReference Include="..\AutoInstaller.Infrastructure\AutoInstaller.Infrastructure.csproj" />
    <ProjectReference Include="..\AutoInstaller.EngineManager\AutoInstaller.EngineManager.csproj" />
    <ProjectReference Include="..\AutoInstaller.Docker\AutoInstaller.Docker.csproj" />
    <ProjectReference Include="..\AutoInstaller.Podman\AutoInstaller.Podman.csproj" />
    <ProjectReference Include="..\AutoInstaller.FallbackSystem\AutoInstaller.FallbackSystem.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Controls\" />
    <Folder Include="Converters\" />
    <Folder Include="Services\" />
    <Folder Include="Styles\" />
    <Folder Include="Assets\" />
    <Folder Include="Resources\" />
  </ItemGroup>

  <ItemGroup>
    <AvaloniaResource Include="Assets\**" />
  </ItemGroup>

</Project>
