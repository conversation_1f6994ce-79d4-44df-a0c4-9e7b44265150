# 🐳 Auto-Instalador Desktop Multiplataforma Autônomo

**Sistema completamente autônomo para gerenciamento de containers com Docker e Podman embarcados, interface moderna estilo Docker Desktop e funcionamento independente de instalações prévias.**

[![.NET](https://img.shields.io/badge/.NET-9.0-blue.svg)](https://dotnet.microsoft.com/)
[![Avalonia](https://img.shields.io/badge/Avalonia-11.3.4-purple.svg)](https://avaloniaui.net/)
[![Docker](https://img.shields.io/badge/Docker-Embedded-blue.svg)](https://www.docker.com/)
[![Podman](https://img.shields.io/badge/Podman-Embedded-orange.svg)](https://podman.io/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## 🚀 Características Principais

- **🔧 Completamente Autônomo**: Funciona sem Docker/Podman pré-instalados
- **📦 Engines Embarcadas**: Binários oficiais do Docker Engine e Podman Engine inclusos
- **🎨 Interface Docker Desktop**: UI Dark/Blue com cards arredondados e controles modernos
- **🔄 Sistema de Fallback**: Detecta e permite escolha entre engines existentes ou embarcadas
- **🐳 Integração Nativa**: Comunicação via Docker.DotNet e PodManClient.DotNet (nunca CLI)
- **🏗️ Arquitetura Limpa**: Clean Architecture com CQRS e 7 agentes especializados
- **📊 Logs em Tempo Real**: Feedback detalhado de todas as operações na interface
- **🌐 Multiplataforma**: Windows 10/11, Linux Ubuntu/Debian com detecção automática
- **⚡ Performance**: Otimizado com .NET 9 e inicialização rápida de engines

## 🔧 Pré-requisitos

### **Sistema Completamente Autônomo**
O Auto-Instalador Desktop **NÃO REQUER** Docker ou Podman pré-instalados! O sistema é completamente autônomo e inclui:

- **✅ Engines Embarcadas**: Binários oficiais do Docker Engine e Podman Engine inclusos
- **✅ Detecção Automática**: Identifica sistema operacional e arquitetura automaticamente
- **✅ Configuração Transparente**: Extrai, configura e inicia engines localmente
- **✅ Sistema de Fallback**: Detecta engines existentes e permite escolha inteligente

### **Requisitos Mínimos do Sistema**
- **Windows**: Windows 10 versão 1903+ ou Windows 11
- **Linux**: Ubuntu 20.04+, Debian 11+, ou distribuições compatíveis
- **RAM**: 4 GB mínimo (8 GB recomendado para melhor performance)
- **Armazenamento**: 2 GB livres para engines embarcadas e dados
- **Permissões**: Usuário com permissões administrativas para inicialização das engines

### **Fluxo de Inicialização Automática**
1. **Detecção de Ambiente**: Sistema operacional, arquitetura e engines existentes
2. **Escolha Inteligente**: Opção de usar engine existente ou embarcada
3. **Extração Automática**: Descompactação de binários apropriados para o SO
4. **Configuração Dinâmica**: Setup de paths, sockets e certificados
5. **Inicialização Local**: Startup das engines como processos gerenciados
6. **Conexão Nativa**: Estabelecimento de comunicação via APIs .NET

**✨ VANTAGEM**: Funciona imediatamente após download, sem instalações adicionais!

## 📋 Funcionalidades

### Sistema Autônomo de Engines
- ✅ **Detecção Automática**: Identifica SO, arquitetura e engines existentes
- ✅ **Engines Embarcadas**: Docker Engine e Podman Engine inclusos
- ✅ **Extração Inteligente**: Descompacta binários corretos automaticamente
- ✅ **Configuração Dinâmica**: Setup automático de paths, sockets e portas
- ✅ **Sistema de Fallback**: Escolha entre engines existentes ou embarcadas
- ✅ **Inicialização Local**: Gerenciamento de engines como processos locais

### Gerenciamento de Containers
- ✅ Criar, iniciar, parar e remover containers (Docker e Podman)
- ✅ Pausar e despausar containers com feedback visual
- ✅ Monitoramento de status em tempo real com indicadores coloridos
- ✅ Visualização de logs com filtros e busca
- ✅ Estatísticas de uso (CPU, memória, rede, I/O)
- ✅ Execução de comandos interativos em containers

### Interface Estilo Docker Desktop
- ✅ **Tema Dark/Blue**: Interface moderna com paleta Docker Desktop
- ✅ **Cards Arredondados**: Componentes com bordas suaves e sombras
- ✅ **Logs Integrados**: Painel de logs em tempo real na interface
- ✅ **Controles Avançados**: Seções expansíveis para configurações
- ✅ **Indicadores Visuais**: Status coloridos (verde/vermelho/amarelo)
- ✅ **Animações Suaves**: Transições de 200-300ms para melhor UX

### Arquitetura e Qualidade
- ✅ **Clean Architecture**: Separação rigorosa de camadas e responsabilidades
- ✅ **CQRS com MediatR**: Commands, Queries e Handlers bem definidos
- ✅ **7 Agentes Especializados**: Sistema coordenado de desenvolvimento
- ✅ **Comunicação Nativa**: Docker.DotNet e PodManClient.DotNet (nunca CLI)
- ✅ **Testes Abrangentes**: Cobertura >80% com validação de engines embarcadas
- ✅ **Logging Estruturado**: Serilog com categorias específicas por componente

## 🛠️ Tecnologias

### Engines Embarcadas
- **Docker Engine** - Binários oficiais embarcados para containerização
- **Podman Engine** - Binários oficiais embarcados como alternativa rootless
- **Docker.DotNet 3.125.15** - Comunicação nativa com Docker Engine (nunca CLI)
- **PodManClient.DotNet 1.0.4** - Comunicação nativa com Podman Engine (nunca CLI)

### Backend (.NET 9)
- **.NET 9.0** - Framework principal com performance otimizada
- **Entity Framework Core** - ORM com SQLite para persistência local
- **MediatR** - CQRS e mediação entre camadas
- **FluentValidation** - Validações robustas de comandos e queries
- **AutoMapper** - Mapeamento automático entre DTOs e entidades
- **Serilog** - Logging estruturado com categorias específicas

### Frontend (Avalonia UI)
- **Avalonia 11.3.4** - Framework UI multiplataforma nativo
- **ReactiveUI** - MVVM reativo com RaiseAndSetIfChanged
- **Tema Docker Desktop** - Paleta Dark/Blue com Material Design
- **XAML** - Linguagem de marcação declarativa
- **Animações Fluent** - Transições suaves de 200-300ms

### Sistema de Agentes
- **7 Agentes Especializados** - Arquitetura coordenada de desenvolvimento
- **Gerente de Agentes** - Coordenação e delegação de tarefas
- **Comunicação Sistemática** - Protocolos padronizados de reporte

### Testes e Qualidade
- **xUnit** - Framework de testes unitários e integração
- **FluentAssertions** - Assertions expressivas e legíveis
- **Moq** - Framework de mocking para isolamento de dependências
- **TestContainers** - Testes de integração com engines embarcadas
- **BenchmarkDotNet** - Testes de performance e benchmarking
- **Coverlet** - Análise de cobertura de código (meta: >80%)

## 🚀 Início Rápido

### Pré-requisitos Mínimos
- **.NET 9 SDK** (apenas para desenvolvimento)
- **Git** (para clonagem do repositório)
- **Sistema Operacional**: Windows 10/11, Ubuntu 20.04+, ou Debian 11+

**✨ NÃO É NECESSÁRIO**: Docker ou Podman pré-instalados!

### Instalação e Execução

1. **Clone o repositório**
```bash
git clone https://github.com/DRS-Developer/auto-instalador-max.git
cd auto-instalador-max
```

2. **Restaure as dependências**
```bash
dotnet restore
```

3. **Execute o Auto-Instalador Autônomo**
```bash
dotnet run --project src/AutoInstaller.UI
```

### Primeira Execução
Na primeira execução, o sistema irá:
1. **Detectar automaticamente** seu sistema operacional e arquitetura
2. **Verificar** se Docker/Podman já estão instalados
3. **Apresentar opções** para usar engines existentes ou embarcadas
4. **Extrair e configurar** as engines embarcadas se necessário
5. **Inicializar** as engines localmente
6. **Conectar** via APIs nativas (.NET)
7. **Exibir interface** estilo Docker Desktop pronta para uso

### Build Multiplataforma

**Windows:**
```powershell
.\build\build.ps1 -Target All -Configuration Release
```

**Linux/macOS:**
```bash
chmod +x build/build.sh
./build/build.sh All Release All
```

## 🧪 Testes

### Executar todos os testes
```bash
dotnet test --configuration Release
```

### Gerar relatório de cobertura
```bash
dotnet test --collect:"XPlat Code Coverage"
dotnet tool run reportgenerator --reports:"**/coverage.cobertura.xml" --targetdir:"coverage-report"
```

## 📁 Estrutura do Projeto

```
auto-instalador-max/
├── src/
│   ├── AutoInstaller.Core/           # Domain Layer (Entidades e Interfaces)
│   ├── AutoInstaller.Application/    # Application Layer (CQRS, Commands, Queries)
│   ├── AutoInstaller.Infrastructure/ # Infrastructure Layer (Repositórios, EF Core)
│   ├── AutoInstaller.UI/            # Presentation Layer (Avalonia UI Dark/Blue)
│   ├── AutoInstaller.EngineManager/  # Gerenciamento de Engines Embarcadas
│   ├── AutoInstaller.Docker/         # Integração Docker.DotNet
│   ├── AutoInstaller.Podman/         # Integração PodManClient.DotNet
│   └── AutoInstaller.FallbackSystem/ # Sistema de Detecção e Fallback
├── engines/
│   ├── docker/
│   │   ├── windows/                  # Binários Docker Engine para Windows
│   │   └── linux/                    # Binários Docker Engine para Linux
│   └── podman/
│       ├── windows/                  # Binários Podman Engine para Windows
│       └── linux/                    # Binários Podman Engine para Linux
├── tests/
│   ├── AutoInstaller.Tests.Unit/    # Testes Unitários (>80% cobertura)
│   ├── AutoInstaller.Integration.Tests/ # Testes de Integração com Engines
│   └── AutoInstaller.UI.Tests/      # Testes de Interface Avalonia
├── build/
│   ├── build.ps1                    # Script de build Windows
│   └── build.sh                     # Script de build Linux/macOS
├── Documentos-Auto-Instalador/      # Documentação técnica atualizada
├── .augment/rules/                  # Regras Augment para 7 agentes
└── .github/workflows/               # CI/CD Pipeline multiplataforma
```

## 🏗️ Arquitetura

O projeto segue os princípios da **Clean Architecture** com **7 Agentes Especializados** coordenados:

### Camadas da Clean Architecture
- **Core**: Entidades de domínio, value objects e interfaces para engines
- **Application**: Commands, queries, handlers (CQRS) e casos de uso
- **Infrastructure**: Repositórios, EF Core, configurações e logging
- **UI**: Interface Avalonia com tema Docker Desktop e MVVM reativo

### Sistema de 7 Agentes Especializados
1. **Agente Docker** - Gerenciamento de Docker Engine embarcado via Docker.DotNet
2. **Agente PodMan** - Gerenciamento de Podman Engine embarcado via PodManClient.DotNet
3. **Agente Avalonia UI** - Interface Dark/Blue estilo Docker Desktop
4. **Agente Clean Architecture CQRS** - Arquitetura, commands, queries e handlers
5. **Agente Infraestrutura** - EF Core, Serilog, configurações e health checks
6. **Agente Testes** - Testes unitários, integração e UI (cobertura >80%)
7. **Agente Deployment** - Build multiplataforma e distribuição

### Módulos Especializados
- **EngineManager**: Coordenação de engines embarcadas e detecção de SO
- **FallbackSystem**: Sistema inteligente de escolha entre engines
- **UIManager**: Controle da interface com logs em tempo real
- **LoggingSystem**: Categorização e estruturação de logs por componente

## 📖 Documentação

A documentação completa está disponível na pasta `Documentos-Auto-Instalador/`:

- [Especificações Técnicas](Documentos-Auto-Instalador/01-ESPECIFICACOES_TECNICAS.md)
- [Arquitetura da UI](Documentos-Auto-Instalador/02-ARQUITETURA_UI.md)
- [Arquitetura Modular](Documentos-Auto-Instalador/03-ARQUITETURA_MODULAR.md)
- [Configurações de Ambiente](Documentos-Auto-Instalador/05-CONFIGURACOES_AMBIENTE.md)
- [Testes e Qualidade](Documentos-Auto-Instalador/07-TESTES_QUALIDADE.md)
- [Deployment e Distribuição](Documentos-Auto-Instalador/08-DEPLOYMENT_DISTRIBUICAO.md)

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 👨‍💻 Autor

**DRS Developer**
- GitHub: [@DRS-Developer](https://github.com/DRS-Developer)
- Email: <EMAIL>

## 🙏 Agradecimentos

- [Avalonia UI](https://avaloniaui.net/) - Framework UI multiplataforma
- [Docker.DotNet](https://github.com/dotnet/Docker.DotNet) - Cliente .NET para Docker
- [MediatR](https://github.com/jbogard/MediatR) - Mediação e CQRS
- [FluentValidation](https://fluentvalidation.net/) - Validações fluentes
