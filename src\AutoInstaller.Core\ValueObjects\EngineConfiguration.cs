using AutoInstaller.Core.Enums;

namespace AutoInstaller.Core.ValueObjects;

/// <summary>
/// Value Object para configurações de engine embarcada
/// </summary>
public class EngineConfiguration : ValueObject
{
    /// <summary>
    /// Porta ou socket para comunicação
    /// </summary>
    public string CommunicationEndpoint { get; }

    /// <summary>
    /// Diretório de dados da engine
    /// </summary>
    public string DataDirectory { get; }

    /// <summary>
    /// Diretório de configuração
    /// </summary>
    public string ConfigDirectory { get; }

    /// <summary>
    /// Diretório de logs
    /// </summary>
    public string LogDirectory { get; }

    /// <summary>
    /// Configurações específicas do Docker
    /// </summary>
    public DockerConfiguration? DockerConfig { get; }

    /// <summary>
    /// Configurações específicas do Podman
    /// </summary>
    public PodmanConfiguration? PodmanConfig { get; }

    /// <summary>
    /// Timeout para operações em segundos
    /// </summary>
    public int OperationTimeoutSeconds { get; }

    /// <summary>
    /// Intervalo de health check em segundos
    /// </summary>
    public int HealthCheckIntervalSeconds { get; }

    /// <summary>
    /// Configurações de recursos
    /// </summary>
    public ResourceLimits ResourceLimits { get; }

    /// <summary>
    /// Configurações de rede
    /// </summary>
    public NetworkConfiguration NetworkConfig { get; }

    /// <summary>
    /// Construtor
    /// </summary>
    public EngineConfiguration(
        string communicationEndpoint = "",
        string dataDirectory = "",
        string configDirectory = "",
        string logDirectory = "",
        DockerConfiguration? dockerConfig = null,
        PodmanConfiguration? podmanConfig = null,
        int operationTimeoutSeconds = 30,
        int healthCheckIntervalSeconds = 10,
        ResourceLimits? resourceLimits = null,
        NetworkConfiguration? networkConfig = null)
    {
        CommunicationEndpoint = communicationEndpoint;
        DataDirectory = dataDirectory;
        ConfigDirectory = configDirectory;
        LogDirectory = logDirectory;
        DockerConfig = dockerConfig;
        PodmanConfig = podmanConfig;
        OperationTimeoutSeconds = operationTimeoutSeconds > 0 ? operationTimeoutSeconds : 30;
        HealthCheckIntervalSeconds = healthCheckIntervalSeconds > 0 ? healthCheckIntervalSeconds : 10;
        ResourceLimits = resourceLimits ?? new ResourceLimits();
        NetworkConfig = networkConfig ?? new NetworkConfiguration();
    }

    /// <summary>
    /// Cria configuração padrão para Docker
    /// </summary>
    public static EngineConfiguration CreateDockerDefault(string baseDirectory)
    {
        var dataDir = Path.Combine(baseDirectory, "docker", "data");
        var configDir = Path.Combine(baseDirectory, "docker", "config");
        var logDir = Path.Combine(baseDirectory, "docker", "logs");

        var dockerConfig = new DockerConfiguration(
            daemonConfigPath: Path.Combine(configDir, "daemon.json"),
            socketPath: Environment.OSVersion.Platform == PlatformID.Win32NT 
                ? "npipe://./pipe/docker_engine" 
                : "unix:///var/run/docker.sock",
            enableBuildKit: true,
            enableExperimental: false
        );

        return new EngineConfiguration(
            communicationEndpoint: dockerConfig.SocketPath,
            dataDirectory: dataDir,
            configDirectory: configDir,
            logDirectory: logDir,
            dockerConfig: dockerConfig,
            operationTimeoutSeconds: 60,
            healthCheckIntervalSeconds: 15
        );
    }

    /// <summary>
    /// Cria configuração padrão para Podman
    /// </summary>
    public static EngineConfiguration CreatePodmanDefault(string baseDirectory)
    {
        var dataDir = Path.Combine(baseDirectory, "podman", "data");
        var configDir = Path.Combine(baseDirectory, "podman", "config");
        var logDir = Path.Combine(baseDirectory, "podman", "logs");

        var podmanConfig = new PodmanConfiguration(
            socketPath: Environment.OSVersion.Platform == PlatformID.Win32NT
                ? "npipe://./pipe/podman_engine"
                : "unix:///run/user/1000/podman/podman.sock",
            rootless: true,
            cgroupManager: "systemd",
            storageDriver: "overlay"
        );

        return new EngineConfiguration(
            communicationEndpoint: podmanConfig.SocketPath,
            dataDirectory: dataDir,
            configDirectory: configDir,
            logDirectory: logDir,
            podmanConfig: podmanConfig,
            operationTimeoutSeconds: 45,
            healthCheckIntervalSeconds: 12
        );
    }

    /// <summary>
    /// Implementação de igualdade para Value Object
    /// </summary>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return CommunicationEndpoint;
        yield return DataDirectory;
        yield return ConfigDirectory;
        yield return LogDirectory;
        yield return DockerConfig ?? new object();
        yield return PodmanConfig ?? new object();
        yield return OperationTimeoutSeconds;
        yield return HealthCheckIntervalSeconds;
        yield return ResourceLimits;
        yield return NetworkConfig;
    }
}

/// <summary>
/// Configurações específicas do Docker
/// </summary>
public class DockerConfiguration : ValueObject
{
    public string DaemonConfigPath { get; }
    public string SocketPath { get; }
    public bool EnableBuildKit { get; }
    public bool EnableExperimental { get; }
    public string? RegistryMirror { get; }
    public string[] InsecureRegistries { get; }

    public DockerConfiguration(
        string daemonConfigPath,
        string socketPath,
        bool enableBuildKit = true,
        bool enableExperimental = false,
        string? registryMirror = null,
        string[]? insecureRegistries = null)
    {
        DaemonConfigPath = daemonConfigPath;
        SocketPath = socketPath;
        EnableBuildKit = enableBuildKit;
        EnableExperimental = enableExperimental;
        RegistryMirror = registryMirror;
        InsecureRegistries = insecureRegistries ?? Array.Empty<string>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return DaemonConfigPath;
        yield return SocketPath;
        yield return EnableBuildKit;
        yield return EnableExperimental;
        yield return RegistryMirror ?? string.Empty;
        foreach (var registry in InsecureRegistries)
            yield return registry;
    }
}

/// <summary>
/// Configurações específicas do Podman
/// </summary>
public class PodmanConfiguration : ValueObject
{
    public string SocketPath { get; }
    public bool Rootless { get; }
    public string CgroupManager { get; }
    public string StorageDriver { get; }
    public string? StorageRoot { get; }
    public string? RunRoot { get; }

    public PodmanConfiguration(
        string socketPath,
        bool rootless = true,
        string cgroupManager = "systemd",
        string storageDriver = "overlay",
        string? storageRoot = null,
        string? runRoot = null)
    {
        SocketPath = socketPath;
        Rootless = rootless;
        CgroupManager = cgroupManager;
        StorageDriver = storageDriver;
        StorageRoot = storageRoot;
        RunRoot = runRoot;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return SocketPath;
        yield return Rootless;
        yield return CgroupManager;
        yield return StorageDriver;
        yield return StorageRoot ?? string.Empty;
        yield return RunRoot ?? string.Empty;
    }
}

/// <summary>
/// Configurações de rede
/// </summary>
public class NetworkConfiguration : ValueObject
{
    public string DefaultNetwork { get; }
    public bool EnableIPv6 { get; }
    public string? BridgeName { get; }
    public string? IPRange { get; }

    public NetworkConfiguration(
        string defaultNetwork = "bridge",
        bool enableIPv6 = false,
        string? bridgeName = null,
        string? ipRange = null)
    {
        DefaultNetwork = defaultNetwork;
        EnableIPv6 = enableIPv6;
        BridgeName = bridgeName;
        IPRange = ipRange;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return DefaultNetwork;
        yield return EnableIPv6;
        yield return BridgeName ?? string.Empty;
        yield return IPRange ?? string.Empty;
    }
}
