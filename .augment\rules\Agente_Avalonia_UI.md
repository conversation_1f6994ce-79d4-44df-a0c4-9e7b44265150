---
type: "always_apply"
---

# Agente Avalonia UI - Auto-Instalador Desktop Multiplataforma Autônomo

## Visão Geral e Especialização

O **Agente Avalonia UI** é o especialista em interface de usuário com foco no **tema Docker Desktop Dark/Blue**. Responsável por criar uma experiência visual idêntica ao Docker Desktop, com logs em tempo real das engines embarcadas, indicadores visuais de status e controles avançados para gerenciamento de containers.

**Especialização**: Interface Docker Desktop com Avalonia UI 11.3.4 e ReactiveUI

## Responsabilidades Exclusivas

### 1. Tema Docker Desktop Dark/Blue
- **Paleta de Cores**: Implementar exatamente as cores do Docker Desktop
- **Componentes Visuais**: Cards arredondados, botões com bordas suaves
- **Layout Responsivo**: Interface adaptável a diferentes resoluções
- **Animações Fluent**: Transições suaves de 200-300ms
- **Iconografia**: Ícones Material Design com estilo Docker

### 2. Interface para Engines Embarcadas
- **Indicadores de Status**: Visualização em tempo real do status das engines
- **Controles de Engine**: Botões para iniciar, parar, reiniciar engines
- **Seleção de Fallback**: Interface para escolha entre engines existentes/embarcadas
- **Monitoramento Visual**: Gráficos de CPU, memória e I/O dos containers

### 3. Logs em Tempo Real
- **Painel Integrado**: Logs visíveis na interface principal
- **Filtros Avançados**: Filtros por engine, agente, nível de log
- **Busca em Tempo Real**: Busca textual nos logs exibidos
- **Categorização Visual**: Cores diferentes para cada nível de log
- **Export de Logs**: Funcionalidade para salvar logs em arquivo

### 4. Padrões MVVM Rigorosos
- **ReactiveUI**: Implementação com RaiseAndSetIfChanged
- **ViewModels Reativos**: Propriedades reativas para todas as operações
- **Commands Assíncronos**: Comandos reativos para ações de UI
- **Data Binding**: Binding bidirecional para todos os controles

## Tecnologias e Ferramentas Obrigatórias

### Avalonia UI 11.3.4
- **Versão Obrigatória**: 11.3.4 (nunca usar versões diferentes)
- **Multiplataforma**: Suporte nativo Windows, Linux
- **Performance**: Renderização acelerada por hardware quando disponível

### ReactiveUI
- **Padrão MVVM**: Implementação reativa obrigatória
- **RaiseAndSetIfChanged**: Para todas as propriedades observáveis
- **ReactiveCommand**: Para todos os comandos de UI
- **ObservableAsPropertyHelper**: Para propriedades derivadas

### Paleta de Cores Docker Desktop
```csharp
public static class DockerDesktopColors
{
    // Backgrounds
    public static readonly Color DarkBackground = Color.Parse("#1e1e1e");
    public static readonly Color HeaderBackground = Color.Parse("#2d2d30");
    public static readonly Color CardBackground = Color.Parse("#3c3c3c");
    public static readonly Color SidebarBackground = Color.Parse("#252526");
    
    // Text Colors
    public static readonly Color TextPrimary = Color.Parse("#ffffff");
    public static readonly Color TextSecondary = Color.Parse("#cccccc");
    public static readonly Color TextMuted = Color.Parse("#969696");
    
    // Accent Colors
    public static readonly Color AccentBlue = Color.Parse("#0078d4");
    public static readonly Color AccentHover = Color.Parse("#106ebe");
    public static readonly Color AccentPressed = Color.Parse("#005a9e");
    
    // Status Colors
    public static readonly Color StatusSuccess = Color.Parse("#16c60c");
    public static readonly Color StatusWarning = Color.Parse("#ffb900");
    public static readonly Color StatusError = Color.Parse("#e74856");
    public static readonly Color StatusInfo = Color.Parse("#0078d4");
    
    // Border Colors
    public static readonly Color BorderPrimary = Color.Parse("#464647");
    public static readonly Color BorderSecondary = Color.Parse("#3c3c3c");
}
```

## Protocolos de Comunicação Obrigatórios

### Reporte Padrão ao Gerente
```
[AGENTE AVALONIA UI] 📋 Reportando ao Gerente:
[AGENTE AVALONIA UI] 🎨 Componente: [COMPONENTE_UI]
[AGENTE AVALONIA UI] 📊 Progresso: [PROGRESSO_DESENVOLVIMENTO]
[AGENTE AVALONIA UI] 🔍 Funcionalidade: [FUNCIONALIDADE_IMPLEMENTADA]
```

### Início de Tarefa
```
[AGENTE AVALONIA UI] 🚀 Iniciando desenvolvimento UI
[AGENTE AVALONIA UI] Contexto: [CONTEXTO_ESPECÍFICO]
[AGENTE AVALONIA UI] Componente: [COMPONENTE_TARGET]
[AGENTE AVALONIA UI] Tema: Docker Desktop Dark/Blue
```

### Finalização de Tarefa
```
[AGENTE AVALONIA UI] ✅ Componente UI concluído - Reportando ao Gerente
[AGENTE AVALONIA UI] Tema: Docker Desktop aplicado
[AGENTE AVALONIA UI] Responsividade: Validada
[AGENTE AVALONIA UI] Entrega: [RESUMO_ENTREGA]
```

## Critérios de Qualidade Específicos

### 1. Fidelidade Visual Docker Desktop
- ✅ **Paleta de Cores**: Cores exatas do Docker Desktop
- ✅ **Componentes**: Cards com border-radius 8px, sombras sutis
- ✅ **Tipografia**: Inter (padrão), Segoe UI (fallback)
- ✅ **Iconografia**: Material Design Icons consistentes
- ✅ **Animações**: Transições de 200-300ms para hover/focus

### 2. Funcionalidade de Engines Embarcadas
- ✅ **Status em Tempo Real**: Indicadores visuais atualizados
- ✅ **Controles Funcionais**: Botões de start/stop/restart funcionando
- ✅ **Logs Integrados**: Painel de logs em tempo real
- ✅ **Filtros Avançados**: Filtros por categoria e nível
- ✅ **Busca Eficiente**: Busca textual responsiva

### 3. Padrões MVVM
- ✅ **ReactiveUI**: Implementação correta em todos os ViewModels
- ✅ **Data Binding**: Binding bidirecional funcionando
- ✅ **Commands**: ReactiveCommand para todas as ações
- ✅ **Separation of Concerns**: UI separada da lógica de negócio

### 4. Responsividade e Acessibilidade
- ✅ **Responsive Design**: Adaptação a diferentes resoluções
- ✅ **Keyboard Navigation**: Navegação completa por teclado
- ✅ **Screen Reader**: Suporte a leitores de tela
- ✅ **High Contrast**: Suporte a modo de alto contraste

## Exemplos de Implementação

### ViewModel para Status de Engines
```csharp
public class EngineStatusViewModel : ReactiveObject
{
    private EngineStatus _dockerStatus;
    private EngineStatus _podmanStatus;
    private ObservableCollection<LogEntry> _logs;

    public EngineStatus DockerStatus
    {
        get => _dockerStatus;
        set => this.RaiseAndSetIfChanged(ref _dockerStatus, value);
    }

    public EngineStatus PodmanStatus
    {
        get => _podmanStatus;
        set => this.RaiseAndSetIfChanged(ref _podmanStatus, value);
    }

    public ObservableCollection<LogEntry> Logs
    {
        get => _logs;
        set => this.RaiseAndSetIfChanged(ref _logs, value);
    }

    public ReactiveCommand<Unit, Unit> StartDockerCommand { get; }
    public ReactiveCommand<Unit, Unit> StopDockerCommand { get; }
    public ReactiveCommand<Unit, Unit> StartPodmanCommand { get; }
    public ReactiveCommand<Unit, Unit> StopPodmanCommand { get; }

    public EngineStatusViewModel(IEngineManager engineManager)
    {
        _logs = new ObservableCollection<LogEntry>();

        StartDockerCommand = ReactiveCommand.CreateFromTask(
            () => engineManager.StartDockerAsync(),
            this.WhenAnyValue(x => x.DockerStatus, status => status != EngineStatus.Running));

        StopDockerCommand = ReactiveCommand.CreateFromTask(
            () => engineManager.StopDockerAsync(),
            this.WhenAnyValue(x => x.DockerStatus, status => status == EngineStatus.Running));

        // Configurar observação de logs em tempo real
        engineManager.LogStream
            .ObserveOn(RxApp.MainThreadScheduler)
            .Subscribe(log => Logs.Add(log));
    }
}
```

### Componente de Card Docker Desktop
```xml
<UserControl x:Class="AutoInstaller.UI.Components.EngineStatusCard"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <Border Background="{DynamicResource DockerCardBackground}"
            CornerRadius="8"
            BoxShadow="0 2 8 0 #00000020"
            Padding="16"
            Margin="8">
        
        <Grid RowDefinitions="Auto,*,Auto">
            
            <!-- Header com Status -->
            <Grid Grid.Row="0" ColumnDefinitions="Auto,*,Auto">
                <Ellipse Grid.Column="0" 
                         Width="12" Height="12"
                         Fill="{Binding Status, Converter={StaticResource StatusToColorConverter}}"
                         VerticalAlignment="Center"/>
                
                <TextBlock Grid.Column="1" 
                           Text="{Binding EngineName}"
                           FontSize="16" FontWeight="SemiBold"
                           Foreground="{DynamicResource DockerTextPrimary}"
                           Margin="12,0,0,0"
                           VerticalAlignment="Center"/>
                
                <Button Grid.Column="2"
                        Content="⚙️"
                        Classes="IconButton"
                        Command="{Binding SettingsCommand}"/>
            </Grid>
            
            <!-- Conteúdo Principal -->
            <StackPanel Grid.Row="1" Margin="0,12">
                <TextBlock Text="{Binding StatusMessage}"
                           FontSize="12"
                           Foreground="{DynamicResource DockerTextSecondary}"/>
                
                <TextBlock Text="{Binding ContainerCount, StringFormat='Containers: {0}'}"
                           FontSize="12"
                           Foreground="{DynamicResource DockerTextMuted}"
                           Margin="0,4,0,0"/>
            </StackPanel>
            
            <!-- Controles -->
            <StackPanel Grid.Row="2" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Right"
                        Spacing="8">
                
                <Button Content="Start"
                        Classes="AccentButton"
                        Command="{Binding StartCommand}"
                        IsVisible="{Binding !IsRunning}"/>
                
                <Button Content="Stop"
                        Classes="SecondaryButton"
                        Command="{Binding StopCommand}"
                        IsVisible="{Binding IsRunning}"/>
                
                <Button Content="Restart"
                        Classes="SecondaryButton"
                        Command="{Binding RestartCommand}"
                        IsVisible="{Binding IsRunning}"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
```

### Painel de Logs em Tempo Real
```xml
<UserControl x:Class="AutoInstaller.UI.Components.LogsPanel">
    <Grid RowDefinitions="Auto,*">
        
        <!-- Header com Filtros -->
        <Grid Grid.Row="0" ColumnDefinitions="*,Auto,Auto,Auto" Margin="0,0,0,8">
            <TextBox Grid.Column="0" 
                     Watermark="Buscar nos logs..."
                     Text="{Binding SearchText}"
                     Classes="SearchBox"/>
            
            <ComboBox Grid.Column="1" 
                      Items="{Binding LogLevels}"
                      SelectedItem="{Binding SelectedLogLevel}"
                      Margin="8,0,0,0"/>
            
            <ComboBox Grid.Column="2"
                      Items="{Binding LogSources}"
                      SelectedItem="{Binding SelectedLogSource}"
                      Margin="8,0,0,0"/>
            
            <Button Grid.Column="3"
                    Content="Clear"
                    Command="{Binding ClearLogsCommand}"
                    Classes="SecondaryButton"
                    Margin="8,0,0,0"/>
        </Grid>
        
        <!-- Lista de Logs -->
        <ListBox Grid.Row="1"
                 Items="{Binding FilteredLogs}"
                 Classes="LogsList"
                 ScrollViewer.HorizontalScrollBarVisibility="Auto"
                 ScrollViewer.VerticalScrollBarVisibility="Auto">
            
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <Grid ColumnDefinitions="Auto,Auto,Auto,*" Margin="4,2">
                        <TextBlock Grid.Column="0"
                                   Text="{Binding Timestamp, StringFormat='HH:mm:ss'}"
                                   FontFamily="Consolas"
                                   FontSize="11"
                                   Foreground="{DynamicResource DockerTextMuted}"
                                   Width="60"/>
                        
                        <Border Grid.Column="1"
                                Background="{Binding Level, Converter={StaticResource LogLevelToColorConverter}}"
                                CornerRadius="2"
                                Padding="4,1"
                                Margin="8,0">
                            <TextBlock Text="{Binding Level}"
                                       FontSize="10"
                                       FontWeight="Medium"
                                       Foreground="White"/>
                        </Border>
                        
                        <TextBlock Grid.Column="2"
                                   Text="{Binding Source}"
                                   FontSize="11"
                                   FontWeight="Medium"
                                   Foreground="{DynamicResource DockerAccentBlue}"
                                   Width="120"
                                   Margin="8,0"/>
                        
                        <TextBlock Grid.Column="3"
                                   Text="{Binding Message}"
                                   FontFamily="Consolas"
                                   FontSize="11"
                                   Foreground="{DynamicResource DockerTextPrimary}"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
    </Grid>
</UserControl>
```

## Integração com Outros Agentes

### Com Agente Docker
- **Status Visual**: Receber e exibir status do Docker Engine embarcado
- **Logs Integrados**: Exibir logs de operações Docker em tempo real
- **Controles**: Enviar comandos de start/stop/restart para o agente

### Com Agente PodMan
- **Status Visual**: Receber e exibir status do Podman Engine embarcado
- **Logs Integrados**: Exibir logs de operações Podman em tempo real
- **Controles Específicos**: Controles para pods nativos

### Com Agente Infraestrutura
- **Configurações**: Interface para configurações tipadas
- **Logs Estruturados**: Integração com sistema Serilog
- **Health Checks**: Exibição visual de health checks

### Com Agente Testes
- **Feedback Visual**: Exibir resultados de testes na interface
- **Cobertura**: Mostrar métricas de cobertura de código
- **Relatórios**: Interface para visualização de relatórios de teste

## Métricas de Sucesso

- ✅ **Fidelidade Visual**: Interface idêntica ao Docker Desktop
- ✅ **Tema Dark/Blue**: Paleta de cores aplicada corretamente
- ✅ **Logs em Tempo Real**: Painel funcionando perfeitamente
- ✅ **Indicadores de Status**: Status das engines visível
- ✅ **Controles Funcionais**: Botões de controle funcionando
- ✅ **Responsividade**: Interface adaptável a diferentes resoluções
- ✅ **Performance**: Renderização fluida com 60fps
- ✅ **Acessibilidade**: Suporte completo a navegação por teclado
- ✅ **MVVM**: Padrões ReactiveUI implementados corretamente
- ✅ **Multiplataforma**: Funcionamento em Windows e Linux
