using AutoInstaller.Core.Enums;
using MediatR;

namespace AutoInstaller.Application.Commands;

/// <summary>
/// Command para iniciar engine embarcada
/// </summary>
public record StartEmbeddedEngineCommand : IRequest<StartEmbeddedEngineResult>
{
    /// <summary>
    /// Nome da engine
    /// </summary>
    public string EngineName { get; init; } = string.Empty;

    /// <summary>
    /// Tipo da engine (Docker/Podman)
    /// </summary>
    public EngineType EngineType { get; init; }

    /// <summary>
    /// Caminho dos binários
    /// </summary>
    public string BinaryPath { get; init; } = string.Empty;

    /// <summary>
    /// Versão da engine
    /// </summary>
    public string Version { get; init; } = string.Empty;

    /// <summary>
    /// Sistema operacional
    /// </summary>
    public OperatingSystem OperatingSystem { get; init; }

    /// <summary>
    /// Arquitetura
    /// </summary>
    public string Architecture { get; init; } = string.Empty;

    /// <summary>
    /// Forçar reinicialização se já estiver rodando
    /// </summary>
    public bool ForceRestart { get; init; } = false;

    /// <summary>
    /// Timeout em segundos
    /// </summary>
    public int TimeoutSeconds { get; init; } = 60;
}

/// <summary>
/// Resultado do comando de iniciar engine embarcada
/// </summary>
public record StartEmbeddedEngineResult
{
    /// <summary>
    /// Indica se a operação foi bem-sucedida
    /// </summary>
    public bool Success { get; init; }

    /// <summary>
    /// ID da engine criada/iniciada
    /// </summary>
    public string EngineId { get; init; } = string.Empty;

    /// <summary>
    /// Mensagem de resultado
    /// </summary>
    public string Message { get; init; } = string.Empty;

    /// <summary>
    /// Endpoint de comunicação
    /// </summary>
    public string? CommunicationEndpoint { get; init; }

    /// <summary>
    /// Process ID da engine
    /// </summary>
    public int? ProcessId { get; init; }

    /// <summary>
    /// Tempo de inicialização em segundos
    /// </summary>
    public double StartupTimeSeconds { get; init; }

    /// <summary>
    /// Erros ocorridos durante a operação
    /// </summary>
    public List<string> Errors { get; init; } = new();

    /// <summary>
    /// Cria resultado de sucesso
    /// </summary>
    public static StartEmbeddedEngineResult CreateSuccess(
        string engineId,
        string message,
        string? communicationEndpoint = null,
        int? processId = null,
        double startupTimeSeconds = 0)
    {
        return new StartEmbeddedEngineResult
        {
            Success = true,
            EngineId = engineId,
            Message = message,
            CommunicationEndpoint = communicationEndpoint,
            ProcessId = processId,
            StartupTimeSeconds = startupTimeSeconds
        };
    }

    /// <summary>
    /// Cria resultado de falha
    /// </summary>
    public static StartEmbeddedEngineResult CreateFailure(string message, List<string>? errors = null)
    {
        return new StartEmbeddedEngineResult
        {
            Success = false,
            Message = message,
            Errors = errors ?? new List<string>()
        };
    }
}

/// <summary>
/// Command para parar engine embarcada
/// </summary>
public record StopEmbeddedEngineCommand : IRequest<StopEmbeddedEngineResult>
{
    /// <summary>
    /// ID da engine
    /// </summary>
    public string EngineId { get; init; } = string.Empty;

    /// <summary>
    /// Nome da engine
    /// </summary>
    public string? EngineName { get; init; }

    /// <summary>
    /// Forçar parada (kill)
    /// </summary>
    public bool Force { get; init; } = false;

    /// <summary>
    /// Timeout em segundos
    /// </summary>
    public int TimeoutSeconds { get; init; } = 30;
}

/// <summary>
/// Resultado do comando de parar engine embarcada
/// </summary>
public record StopEmbeddedEngineResult
{
    /// <summary>
    /// Indica se a operação foi bem-sucedida
    /// </summary>
    public bool Success { get; init; }

    /// <summary>
    /// ID da engine parada
    /// </summary>
    public string EngineId { get; init; } = string.Empty;

    /// <summary>
    /// Mensagem de resultado
    /// </summary>
    public string Message { get; init; } = string.Empty;

    /// <summary>
    /// Tempo de parada em segundos
    /// </summary>
    public double ShutdownTimeSeconds { get; init; }

    /// <summary>
    /// Erros ocorridos durante a operação
    /// </summary>
    public List<string> Errors { get; init; } = new();

    /// <summary>
    /// Cria resultado de sucesso
    /// </summary>
    public static StopEmbeddedEngineResult CreateSuccess(
        string engineId,
        string message,
        double shutdownTimeSeconds = 0)
    {
        return new StopEmbeddedEngineResult
        {
            Success = true,
            EngineId = engineId,
            Message = message,
            ShutdownTimeSeconds = shutdownTimeSeconds
        };
    }

    /// <summary>
    /// Cria resultado de falha
    /// </summary>
    public static StopEmbeddedEngineResult CreateFailure(string engineId, string message, List<string>? errors = null)
    {
        return new StopEmbeddedEngineResult
        {
            Success = false,
            EngineId = engineId,
            Message = message,
            Errors = errors ?? new List<string>()
        };
    }
}

/// <summary>
/// Command para criar container
/// </summary>
public record CreateContainerCommand : IRequest<CreateContainerResult>
{
    /// <summary>
    /// Nome do container
    /// </summary>
    public string Name { get; init; } = string.Empty;

    /// <summary>
    /// Nome da imagem
    /// </summary>
    public string ImageName { get; init; } = string.Empty;

    /// <summary>
    /// Tag da imagem
    /// </summary>
    public string ImageTag { get; init; } = "latest";

    /// <summary>
    /// ID da engine embarcada
    /// </summary>
    public string EmbeddedEngineId { get; init; } = string.Empty;

    /// <summary>
    /// Comando a executar
    /// </summary>
    public string? Command { get; init; }

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public string[]? Arguments { get; init; }

    /// <summary>
    /// Diretório de trabalho
    /// </summary>
    public string? WorkingDirectory { get; init; }

    /// <summary>
    /// Variáveis de ambiente
    /// </summary>
    public Dictionary<string, string> Environment { get; init; } = new();

    /// <summary>
    /// Mapeamentos de porta (formato: "hostPort:containerPort")
    /// </summary>
    public List<string> Ports { get; init; } = new();

    /// <summary>
    /// Montagens de volume (formato: "hostPath:containerPath:mode")
    /// </summary>
    public List<string> Volumes { get; init; } = new();

    /// <summary>
    /// Política de restart
    /// </summary>
    public RestartPolicy RestartPolicy { get; init; } = RestartPolicy.No;

    /// <summary>
    /// Iniciar automaticamente após criação
    /// </summary>
    public bool AutoStart { get; init; } = true;
}

/// <summary>
/// Resultado do comando de criar container
/// </summary>
public record CreateContainerResult
{
    /// <summary>
    /// Indica se a operação foi bem-sucedida
    /// </summary>
    public bool Success { get; init; }

    /// <summary>
    /// ID do container criado
    /// </summary>
    public string ContainerId { get; init; } = string.Empty;

    /// <summary>
    /// ID do container na engine
    /// </summary>
    public string? EngineContainerId { get; init; }

    /// <summary>
    /// Mensagem de resultado
    /// </summary>
    public string Message { get; init; } = string.Empty;

    /// <summary>
    /// Status atual do container
    /// </summary>
    public ContainerStatus Status { get; init; }

    /// <summary>
    /// Erros ocorridos durante a operação
    /// </summary>
    public List<string> Errors { get; init; } = new();

    /// <summary>
    /// Cria resultado de sucesso
    /// </summary>
    public static CreateContainerResult CreateSuccess(
        string containerId,
        string message,
        ContainerStatus status,
        string? engineContainerId = null)
    {
        return new CreateContainerResult
        {
            Success = true,
            ContainerId = containerId,
            EngineContainerId = engineContainerId,
            Message = message,
            Status = status
        };
    }

    /// <summary>
    /// Cria resultado de falha
    /// </summary>
    public static CreateContainerResult CreateFailure(string message, List<string>? errors = null)
    {
        return new CreateContainerResult
        {
            Success = false,
            Message = message,
            Errors = errors ?? new List<string>()
        };
    }
}
