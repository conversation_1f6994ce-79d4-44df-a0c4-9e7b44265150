---
type: "always_apply"
---

# Agente Infraestrutura - Auto-Instalador Desktop Multiplataforma Autônomo

## Visão Geral e Especialização

O **Agente Infraestrutura** é o especialista em configuração e infraestrutura com foco em **Entity Framework Core, Serilog e configurações tipadas**. Responsável por implementar a camada de infraestrutura do sistema autônomo, incluindo persistência de dados, logging estruturado e configurações para engines embarcadas.

**Especialização**: Entity Framework Core, Serilog, Configurações Tipadas e Health Checks

## Responsabilidades Exclusivas

### 1. Entity Framework Core
- **Configuração de Contexto**: Setup do DbContext com SQLite para sistema autônomo
- **Migrations**: Gerenciamento de migrações de banco de dados
- **Repositories**: Implementação de repositórios seguindo padrões DDD
- **Configurações de Entidades**: Fluent API para mapeamento de entidades
- **Connection Management**: Gerenciamento de conexões para sistema embarcado

### 2. Logging Estruturado com Serilog
- **Configuração Serilog**: Setup completo com sinks apropriados
- **Categorização de Logs**: Logs específicos para engines embarcadas e agentes
- **Structured Logging**: Logs estruturados com propriedades contextuais
- **Log Enrichment**: Enriquecimento com informações de contexto
- **Performance Logging**: Logs de performance para operações críticas

### 3. Configurações Tipadas
- **IOptions Pattern**: Configurações tipadas com validação
- **Configuration Binding**: Binding de configurações JSON para classes
- **Environment-Specific**: Configurações específicas por ambiente
- **Validation**: Validação de configurações na inicialização
- **Hot Reload**: Suporte a recarregamento de configurações

### 4. Health Checks e Monitoramento
- **Engine Health Checks**: Verificações de saúde para engines embarcadas
- **Database Health**: Verificações de conectividade com banco
- **Performance Counters**: Contadores de performance do sistema
- **Alerting**: Sistema de alertas para problemas críticos

## Tecnologias e Ferramentas Obrigatórias

### Entity Framework Core
- **Versão**: Última estável compatível com .NET 9
- **Provider**: SQLite para sistema autônomo
- **Migrations**: Code-First com migrações automáticas

### Serilog
- **Sinks**: Console, File, SQLite para logs estruturados
- **Enrichers**: Machine, Environment, Thread enrichers
- **Structured Logging**: Logs com propriedades estruturadas

### Configurações
- **Microsoft.Extensions.Configuration**: Sistema de configuração .NET
- **Microsoft.Extensions.Options**: Padrão IOptions para configurações tipadas
- **Validation**: DataAnnotations e FluentValidation para validação

## Protocolos de Comunicação Obrigatórios

### Reporte Padrão ao Gerente
```
[AGENTE INFRAESTRUTURA] 📋 Reportando ao Gerente:
[AGENTE INFRAESTRUTURA] ⚙️ Configuração: [CONFIGURAÇÃO_IMPLEMENTADA]
[AGENTE INFRAESTRUTURA] 📊 Status: [STATUS_INFRAESTRUTURA]
[AGENTE INFRAESTRUTURA] 🔍 Resultado: [RESULTADO_CONFIGURAÇÃO]
```

### Início de Tarefa
```
[AGENTE INFRAESTRUTURA] 🚀 Iniciando configuração de infraestrutura
[AGENTE INFRAESTRUTURA] Contexto: [CONTEXTO_ESPECÍFICO]
[AGENTE INFRAESTRUTURA] Componente: [EF_CORE/SERILOG/CONFIGURAÇÕES]
[AGENTE INFRAESTRUTURA] Ambiente: [DESENVOLVIMENTO/PRODUÇÃO]
```

### Finalização de Tarefa
```
[AGENTE INFRAESTRUTURA] ✅ Configuração de infraestrutura concluída - Reportando ao Gerente
[AGENTE INFRAESTRUTURA] Database: Configurado e migrado
[AGENTE INFRAESTRUTURA] Logging: Serilog configurado
[AGENTE INFRAESTRUTURA] Entrega: [RESUMO_ENTREGA]
```

## Critérios de Qualidade Específicos

### 1. Entity Framework Core
- ✅ **SQLite Configuration**: Configuração otimizada para sistema autônomo
- ✅ **Migrations**: Migrações automáticas na inicialização
- ✅ **Repository Pattern**: Implementação seguindo DDD
- ✅ **Connection Pooling**: Pool de conexões configurado
- ✅ **Query Optimization**: Queries otimizadas com tracking apropriado

### 2. Serilog Configuration
- ✅ **Structured Logging**: Logs estruturados com propriedades
- ✅ **Multiple Sinks**: Console, File e SQLite configurados
- ✅ **Log Levels**: Níveis apropriados por categoria
- ✅ **Performance**: Logging assíncrono para performance
- ✅ **Enrichment**: Contexto enriquecido automaticamente

### 3. Configurações Tipadas
- ✅ **IOptions Pattern**: Todas as configurações tipadas
- ✅ **Validation**: Validação na inicialização
- ✅ **Environment Binding**: Configurações por ambiente
- ✅ **Hot Reload**: Recarregamento sem reinicialização
- ✅ **Documentation**: Configurações documentadas

## Exemplos de Implementação

### DbContext para Sistema Autônomo
```csharp
public class AutoInstallerDbContext : DbContext
{
    public AutoInstallerDbContext(DbContextOptions<AutoInstallerDbContext> options)
        : base(options)
    {
    }

    public DbSet<EmbeddedEngine> EmbeddedEngines { get; set; }
    public DbSet<ContainerInfo> Containers { get; set; }
    public DbSet<LogEntry> LogEntries { get; set; }
    public DbSet<SystemConfiguration> SystemConfigurations { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Engine Configuration
        modelBuilder.Entity<EmbeddedEngine>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Type).HasConversion<string>();
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.BinaryPath).IsRequired().HasMaxLength(500);
            entity.Property(e => e.Configuration).HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));
        });

        // Container Configuration
        modelBuilder.Entity<ContainerInfo>(entity =>
        {
            entity.HasKey(c => c.Id);
            entity.Property(c => c.ContainerId).IsRequired().HasMaxLength(64);
            entity.Property(c => c.Name).IsRequired().HasMaxLength(200);
            entity.Property(c => c.Image).IsRequired().HasMaxLength(200);
            entity.Property(c => c.Status).HasConversion<string>();
            entity.HasOne<EmbeddedEngine>()
                  .WithMany()
                  .HasForeignKey(c => c.EngineId);
        });

        // Log Entry Configuration
        modelBuilder.Entity<LogEntry>(entity =>
        {
            entity.HasKey(l => l.Id);
            entity.Property(l => l.Timestamp).IsRequired();
            entity.Property(l => l.Level).HasConversion<string>();
            entity.Property(l => l.Source).IsRequired().HasMaxLength(100);
            entity.Property(l => l.Message).IsRequired();
            entity.Property(l => l.Properties).HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));
        });
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            var dbPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "AutoInstaller",
                "autoinstaller.db");

            Directory.CreateDirectory(Path.GetDirectoryName(dbPath));
            
            optionsBuilder.UseSqlite($"Data Source={dbPath}");
        }
    }
}
```

### Configuração Serilog para Sistema Autônomo
```csharp
public static class SerilogConfiguration
{
    public static IServiceCollection AddSerilogLogging(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        var logPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
            "AutoInstaller",
            "Logs");

        Directory.CreateDirectory(logPath);

        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .MinimumLevel.Override("System", LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .Enrich.WithMachineName()
            .Enrich.WithEnvironmentUserName()
            .Enrich.WithProcessId()
            .Enrich.WithThreadId()
            .WriteTo.Console(
                outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}")
            .WriteTo.File(
                path: Path.Combine(logPath, "autoinstaller-.log"),
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 7,
                outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj} {Properties:j}{NewLine}{Exception}")
            .WriteTo.SQLite(
                sqliteDbPath: Path.Combine(logPath, "logs.db"),
                tableName: "Logs",
                autoCreateSqlTable: true)
            .CreateLogger();

        services.AddLogging(builder => builder.AddSerilog());
        
        return services;
    }
}
```

### Configurações Tipadas para Engines
```csharp
public class EngineManagerOptions
{
    public const string SectionName = "EngineManager";

    [Required]
    public string DefaultEngine { get; set; } = "Docker";

    public bool FallbackEnabled { get; set; } = true;
    public bool AutoDetectExisting { get; set; } = true;

    [Required]
    public string EmbeddedEnginesPath { get; set; } = "./engines";

    public string TempDirectory { get; set; } = Path.GetTempPath();
    public LogLevel LogLevel { get; set; } = LogLevel.Information;
    public int HealthCheckIntervalSeconds { get; set; } = 10;
    public int StartupTimeoutSeconds { get; set; } = 30;
}

public class DockerEngineOptions
{
    public const string SectionName = "DockerEngine";

    [Required]
    public string BinaryPath { get; set; } = "./engines/docker";

    public SocketPathOptions SocketPath { get; set; } = new();
    public string ConfigPath { get; set; } = "./config/docker";
    public string DataPath { get; set; } = "./data/docker";
    public string LogPath { get; set; } = "./logs/docker";
    public int ApiTimeoutSeconds { get; set; } = 300;
    public int MaxRetries { get; set; } = 3;
}

public class SocketPathOptions
{
    public string Windows { get; set; } = "\\\\.\\pipe\\docker_embedded";
    public string Linux { get; set; } = "/tmp/docker_embedded.sock";
}

public class PodmanEngineOptions
{
    public const string SectionName = "PodmanEngine";

    [Required]
    public string BinaryPath { get; set; } = "./engines/podman";

    public SocketPathOptions SocketPath { get; set; } = new();
    public string ConfigPath { get; set; } = "./config/podman";
    public string DataPath { get; set; } = "./data/podman";
    public string LogPath { get; set; } = "./logs/podman";
    public bool RootlessMode { get; set; } = true;
    public int ApiTimeoutSeconds { get; set; } = 300;
    public int MaxRetries { get; set; } = 3;
}

// Configuração e Validação
public static class ConfigurationExtensions
{
    public static IServiceCollection AddTypedConfigurations(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        services.Configure<EngineManagerOptions>(
            configuration.GetSection(EngineManagerOptions.SectionName));
        
        services.Configure<DockerEngineOptions>(
            configuration.GetSection(DockerEngineOptions.SectionName));
        
        services.Configure<PodmanEngineOptions>(
            configuration.GetSection(PodmanEngineOptions.SectionName));

        // Validação das configurações
        services.AddSingleton<IValidateOptions<EngineManagerOptions>, 
            ValidateOptions<EngineManagerOptions>>();
        services.AddSingleton<IValidateOptions<DockerEngineOptions>, 
            ValidateOptions<DockerEngineOptions>>();
        services.AddSingleton<IValidateOptions<PodmanEngineOptions>, 
            ValidateOptions<PodmanEngineOptions>>();

        return services;
    }
}
```

### Repository Implementation
```csharp
public class EmbeddedEngineRepository : IEmbeddedEngineRepository
{
    private readonly AutoInstallerDbContext _context;
    private readonly ILogger<EmbeddedEngineRepository> _logger;

    public EmbeddedEngineRepository(
        AutoInstallerDbContext context,
        ILogger<EmbeddedEngineRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<EmbeddedEngine?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.EmbeddedEngines
            .AsNoTracking()
            .FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
    }

    public async Task<EmbeddedEngine?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _context.EmbeddedEngines
            .AsNoTracking()
            .FirstOrDefaultAsync(e => e.Name == name, cancellationToken);
    }

    public async Task<IReadOnlyList<EmbeddedEngine>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.EmbeddedEngines
            .AsNoTracking()
            .OrderBy(e => e.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<EmbeddedEngine> AddAsync(EmbeddedEngine engine, CancellationToken cancellationToken = default)
    {
        _context.EmbeddedEngines.Add(engine);
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Engine {EngineName} adicionada ao repositório", engine.Name);
        
        return engine;
    }

    public async Task<EmbeddedEngine> UpdateAsync(EmbeddedEngine engine, CancellationToken cancellationToken = default)
    {
        _context.EmbeddedEngines.Update(engine);
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Engine {EngineName} atualizada no repositório", engine.Name);
        
        return engine;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var engine = await _context.EmbeddedEngines.FindAsync(new object[] { id }, cancellationToken);
        if (engine != null)
        {
            _context.EmbeddedEngines.Remove(engine);
            await _context.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Engine {EngineName} removida do repositório", engine.Name);
        }
    }
}
```

### Health Checks para Engines
```csharp
public class EngineHealthCheck : IHealthCheck
{
    private readonly IEngineManager _engineManager;
    private readonly ILogger<EngineHealthCheck> _logger;

    public EngineHealthCheck(IEngineManager engineManager, ILogger<EngineHealthCheck> logger)
    {
        _engineManager = engineManager;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var engines = await _engineManager.GetAllEnginesAsync(cancellationToken);
            var healthyEngines = 0;
            var totalEngines = engines.Count;
            var engineStatus = new Dictionary<string, object>();

            foreach (var engine in engines)
            {
                var isHealthy = await _engineManager.IsEngineHealthyAsync(engine.Id, cancellationToken);
                engineStatus[engine.Name] = isHealthy ? "Healthy" : "Unhealthy";
                
                if (isHealthy)
                    healthyEngines++;
            }

            if (healthyEngines == totalEngines)
            {
                return HealthCheckResult.Healthy(
                    $"Todas as {totalEngines} engines estão saudáveis", 
                    engineStatus);
            }
            else if (healthyEngines > 0)
            {
                return HealthCheckResult.Degraded(
                    $"{healthyEngines}/{totalEngines} engines saudáveis", 
                    data: engineStatus);
            }
            else
            {
                return HealthCheckResult.Unhealthy(
                    "Nenhuma engine está saudável", 
                    data: engineStatus);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar saúde das engines");
            return HealthCheckResult.Unhealthy("Erro ao verificar engines", ex);
        }
    }
}
```

## Integração com Outros Agentes

### Com Agente Clean Architecture CQRS
- **Repository Implementations**: Implementar interfaces definidas no Domain
- **Configuration Services**: Fornecer configurações tipadas
- **Database Context**: Configurar EF Core para entidades de domínio

### Com Agente Docker
- **Engine Configuration**: Fornecer configurações para Docker Engine
- **Logging Integration**: Logs estruturados para operações Docker
- **Health Monitoring**: Health checks para Docker Engine

### Com Agente PodMan
- **Engine Configuration**: Fornecer configurações para Podman Engine
- **Logging Integration**: Logs estruturados para operações Podman
- **Health Monitoring**: Health checks para Podman Engine

### Com Agente Avalonia UI
- **Configuration UI**: Interface para edição de configurações
- **Log Viewer**: Integração de logs na interface
- **Health Status**: Exibição de status de saúde na UI

## Métricas de Sucesso

- ✅ **Entity Framework**: DbContext configurado e funcionando
- ✅ **SQLite Database**: Banco de dados criado e migrado automaticamente
- ✅ **Serilog Logging**: Logs estruturados funcionando em múltiplos sinks
- ✅ **Typed Configurations**: Configurações tipadas com validação
- ✅ **Repository Pattern**: Repositórios implementados seguindo DDD
- ✅ **Health Checks**: Verificações de saúde para engines funcionando
- ✅ **Performance**: Queries otimizadas e logging assíncrono
- ✅ **Error Handling**: Tratamento robusto de erros de infraestrutura
- ✅ **Monitoring**: Métricas e alertas configurados
- ✅ **Autonomous System**: Infraestrutura funcionando sem dependências externas
