# Configurações de Ambiente - Auto-Instalador Desktop Multiplataforma Autônomo

## Índice
1. [Requisitos do Sistema Autônomo](#requisitos-do-sistema-autônomo)
2. [Configuração do Ambiente de Desenvolvimento](#configuração-do-ambiente-de-desenvolvimento)
3. [Configurações de Engines Embarcadas](#configurações-de-engines-embarcadas)
4. [Configurações dos 7 Agentes](#configurações-dos-7-agentes)
5. [Variáveis de Ambiente](#variáveis-de-ambiente)
6. [Configurações de Build Multiplataforma](#configurações-de-build-multiplataforma)
7. [Configurações de Deploy Autônomo](#configurações-de-deploy-autônomo)
8. [Configurações de Testes com Engines](#configurações-de-testes-com-engines)
9. [Sistema de Fallback](#sistema-de-fallback)
10. [Troubleshooting Engines Embarcadas](#troubleshooting-engines-embarcadas)

---

## Requisitos do Sistema Autônomo

### Desenvolvimento
- **Sistema Operacional**: Windows 10/11 (1903+), Ubuntu 20.04+, Debian 11+
- **.NET SDK**: 9.0 ou superior
- **IDE**: Visual Studio 2022, JetBrains Rider, ou VS Code
- **Git**: 2.50.0 ou superior
- **Docker/Podman**: OPCIONAL - sistema detecta automaticamente se existentes
- **Node.js**: 24.4.1 (para ferramentas de build)

### Produção (Sistema Completamente Autônomo)
- **Sistema Operacional**: Windows 10/11 (1903+), Ubuntu 20.04+, Debian 11+
- **Memória RAM**: Mínimo 4GB, Recomendado 8GB+ (para engines embarcadas)
- **Espaço em Disco**: Mínimo 2GB livres (engines + dados)
- **Permissões**: Usuário com privilégios administrativos
- **Arquitetura**: x64 (AMD64) - suporte inicial
- **Docker/Podman**: NÃO REQUERIDO - engines embarcadas incluídas

### Engines Embarcadas Incluídas
- **Docker Engine**: Binários oficiais para Windows e Linux
- **Podman Engine**: Binários oficiais para Windows e Linux
- **Detecção Automática**: Sistema operacional, arquitetura, engines existentes
- **Sistema de Fallback**: Escolha inteligente entre engines existentes/embarcadas

---

## Configuração do Ambiente de Desenvolvimento

### 1. Instalação do .NET 9

```bash
# Windows (via winget)
winget install Microsoft.DotNet.SDK.9

# macOS (via Homebrew)
brew install --cask dotnet-sdk

# Ubuntu
wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt-get update
sudo apt-get install -y dotnet-sdk-9.0
```

### 2. Configuração do Git

```bash
git config --global user.name "Seu Nome"
git config --global user.email "<EMAIL>"
git config --global init.defaultBranch main
git config --global pull.rebase false
```

### 3. Clonagem e Configuração do Projeto

```bash
# Clonar o repositório
git clone https://github.com/seu-usuario/auto-instalador-br.git
cd auto-instalador-br

# Restaurar dependências
dotnet restore

# Verificar se tudo está funcionando
dotnet build
```

### 4. Configuração Opcional do Docker/Podman (Para Desenvolvimento)

```bash
# Docker (OPCIONAL - para desenvolvimento)
docker --version
docker-compose --version
docker run hello-world

# Podman (OPCIONAL - para desenvolvimento)
podman --version
podman run hello-world
```

**Nota**: Para usuários finais, Docker/Podman NÃO são necessários - o sistema é completamente autônomo.

---

## Configurações de Engines Embarcadas

### 1. Estrutura de Engines
```
engines/
├── docker/
│   ├── windows/
│   │   ├── dockerd.exe              # Docker Daemon
│   │   ├── docker.exe               # Docker CLI
│   │   └── docker-init.exe          # Init Process
│   └── linux/
│       ├── dockerd                  # Docker Daemon
│       ├── docker                   # Docker CLI
│       └── docker-init              # Init Process
└── podman/
    ├── windows/
    │   ├── podman.exe               # Podman Engine
    │   └── conmon.exe               # Container Monitor
    └── linux/
        ├── podman                   # Podman Engine
        └── conmon                   # Container Monitor
```

### 2. Configuração de Engines Embarcadas
```json
// appsettings.json - Configurações de Engines
{
  "EngineManager": {
    "DefaultEngine": "Docker",
    "FallbackEnabled": true,
    "AutoDetectExisting": true,
    "EmbeddedEnginesPath": "./engines",
    "TempDirectory": "%TEMP%/auto-installer-engines",
    "LogLevel": "Information"
  },
  "DockerEngine": {
    "BinaryPath": "./engines/docker",
    "SocketPath": {
      "Windows": "\\\\.\\pipe\\docker_embedded",
      "Linux": "/tmp/docker_embedded.sock"
    },
    "ConfigPath": "./config/docker",
    "DataPath": "./data/docker",
    "LogPath": "./logs/docker"
  },
  "PodmanEngine": {
    "BinaryPath": "./engines/podman",
    "SocketPath": {
      "Windows": "\\\\.\\pipe\\podman_embedded",
      "Linux": "/tmp/podman_embedded.sock"
    },
    "ConfigPath": "./config/podman",
    "DataPath": "./data/podman",
    "LogPath": "./logs/podman",
    "RootlessMode": true
  },
  "FallbackSystem": {
    "DetectionTimeout": 5000,
    "PreferExisting": false,
    "ShowSelectionDialog": true,
    "RememberChoice": true
  }
}
```

### 3. Configuração dos 7 Agentes
```json
// agents.config.json
{
  "AgentManager": {
    "MaxConcurrentAgents": 3,
    "DefaultTimeout": 300000,
    "LogLevel": "Information"
  },
  "Agents": {
    "DockerAgent": {
      "Enabled": true,
      "Priority": 1,
      "Specialization": "Docker Engine Management",
      "MaxRetries": 3
    },
    "PodmanAgent": {
      "Enabled": true,
      "Priority": 1,
      "Specialization": "Podman Engine Management",
      "MaxRetries": 3
    },
    "AvaloniaUIAgent": {
      "Enabled": true,
      "Priority": 2,
      "Specialization": "Docker Desktop UI Theme",
      "MaxRetries": 2
    },
    "CleanArchitectureAgent": {
      "Enabled": true,
      "Priority": 3,
      "Specialization": "CQRS and Architecture",
      "MaxRetries": 2
    },
    "InfrastructureAgent": {
      "Enabled": true,
      "Priority": 3,
      "Specialization": "EF Core and Configuration",
      "MaxRetries": 2
    },
    "TestsAgent": {
      "Enabled": true,
      "Priority": 4,
      "Specialization": "Quality and Testing",
      "MaxRetries": 1
    },
    "DeploymentAgent": {
      "Enabled": true,
      "Priority": 5,
      "Specialization": "Multiplatform Deployment",
      "MaxRetries": 1
    }
  }
}
```

---

## Configurações de Projeto

### Directory.Build.props

```xml
<Project>
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
  </PropertyGroup>

  <PropertyGroup>
    <Company>Auto-Instalador BR</Company>
    <Product>Auto-Instalador Desktop</Product>
    <Copyright>Copyright © 2024 Auto-Instalador BR</Copyright>
    <Version>1.0.0</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>
</Project>
```

### Directory.Packages.props

```xml
<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <CentralPackageTransitivePinningEnabled>true</CentralPackageTransitivePinningEnabled>
  </PropertyGroup>

  <ItemGroup>
    <!-- Avalonia UI -->
    <PackageVersion Include="Avalonia" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Desktop" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Themes.Fluent" Version="11.3.4" />
    <PackageVersion Include="Avalonia.ReactiveUI" Version="11.3.4" />
    <PackageVersion Include="Avalonia.Diagnostics" Version="11.3.4" />
    
    <!-- Docker Integration -->
    <PackageVersion Include="Docker.DotNet" Version="3.125.15" />
    <PackageVersion Include="Docker.DotNet.X509" Version="3.125.15" />
    
    <!-- Podman Integration -->
    <PackageVersion Include="PodmanClient.DotNet" Version="1.0.4" />
    
    <!-- Dependency Injection -->
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
<PackageVersion Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
<PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
<PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
<PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.0" />
<PackageVersion Include="Microsoft.Extensions.Logging.Console" Version="9.0.0" />
    
    <!-- MediatR -->
    <PackageVersion Include="MediatR" Version="13.0.0" />
    
    <!-- Entity Framework -->
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0" />
    
    <!-- Reactive Extensions -->
    <PackageVersion Include="ReactiveUI" Version="20.1.63" />
    <PackageVersion Include="ReactiveUI.Fody" Version="19.5.41" />
    <PackageVersion Include="DynamicData" Version="9.0.4" />
    
    <!-- Validation -->
    <PackageVersion Include="FluentValidation" Version="12.0.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
    
    <!-- Serialization -->
    <PackageVersion Include="System.Text.Json" Version="9.0.0" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    
    <!-- Testing -->
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.1.4" />
    <PackageVersion Include="FluentAssertions" Version="6.12.1" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.0" />
  </ItemGroup>
</Project>
```

### global.json

```json
{
  "sdk": {
    "version": "9.0.100",
    "rollForward": "latestMinor"
  },
  "msbuild-sdks": {
    "Microsoft.Build.Traversal": "3.4.0"
  }
}
```

---

## Variáveis de Ambiente

### Desenvolvimento (.env.development)

```env
# Aplicação
APP_NAME=Auto-Instalador Desktop
APP_VERSION=1.0.0
APP_ENVIRONMENT=Development

# Logging
LOG_LEVEL=Debug
LOG_FILE_PATH=./logs/app-{Date}.log
LOG_CONSOLE_ENABLED=true
LOG_FILE_ENABLED=true

# Database
DATABASE_CONNECTION_STRING=Data Source=./data/autoinstaller.db
DATABASE_ENABLE_SENSITIVE_DATA_LOGGING=true
DATABASE_ENABLE_DETAILED_ERRORS=true

# Docker
DOCKER_HOST=npipe://./pipe/docker_engine
DOCKER_API_VERSION=1.43
DOCKER_TIMEOUT_SECONDS=30
DOCKER_ENABLE_LOGGING=true

# Podman
PODMAN_CONNECTION_URI=unix:///run/user/1000/podman/podman.sock
PODMAN_API_VERSION=4.0
PODMAN_TIMEOUT_SECONDS=30
PODMAN_ENABLE_LOGGING=true

# UI
UI_THEME=Dark
UI_LANGUAGE=pt-BR
UI_ENABLE_ANIMATIONS=true
UI_ENABLE_TRANSITIONS=true
UI_WINDOW_WIDTH=1200
UI_WINDOW_HEIGHT=800
UI_WINDOW_MIN_WIDTH=800
UI_WINDOW_MIN_HEIGHT=600

# Features
FEATURE_AUTO_UPDATE=true
FEATURE_TELEMETRY=false
FEATURE_CRASH_REPORTING=true
FEATURE_PERFORMANCE_MONITORING=true

# Security
SECURITY_ENABLE_HTTPS=true
SECURITY_CERTIFICATE_PATH=
SECURITY_CERTIFICATE_PASSWORD=
```

### Produção (.env.production)

```env
# Aplicação
APP_NAME=Auto-Instalador Desktop
APP_VERSION=1.0.0
APP_ENVIRONMENT=Production

# Logging
LOG_LEVEL=Information
LOG_FILE_PATH=./logs/app-{Date}.log
LOG_CONSOLE_ENABLED=false
LOG_FILE_ENABLED=true
LOG_MAX_FILE_SIZE=10MB
LOG_MAX_FILES=30

# Database
DATABASE_CONNECTION_STRING=Data Source=./data/autoinstaller.db
DATABASE_ENABLE_SENSITIVE_DATA_LOGGING=false
DATABASE_ENABLE_DETAILED_ERRORS=false

# Docker
DOCKER_HOST=npipe://./pipe/docker_engine
DOCKER_API_VERSION=1.43
DOCKER_TIMEOUT_SECONDS=60
DOCKER_ENABLE_LOGGING=false

# Podman
PODMAN_CONNECTION_URI=unix:///run/user/1000/podman/podman.sock
PODMAN_API_VERSION=4.0
PODMAN_TIMEOUT_SECONDS=60
PODMAN_ENABLE_LOGGING=false

# UI
UI_THEME=Dark
UI_LANGUAGE=pt-BR
UI_ENABLE_ANIMATIONS=true
UI_ENABLE_TRANSITIONS=true
UI_WINDOW_WIDTH=1200
UI_WINDOW_HEIGHT=800
UI_WINDOW_MIN_WIDTH=800
UI_WINDOW_MIN_HEIGHT=600

# Features
FEATURE_AUTO_UPDATE=true
FEATURE_TELEMETRY=true
FEATURE_CRASH_REPORTING=true
FEATURE_PERFORMANCE_MONITORING=false

# Security
SECURITY_ENABLE_HTTPS=true
SECURITY_CERTIFICATE_PATH=./certs/app.pfx
SECURITY_CERTIFICATE_PASSWORD=${CERT_PASSWORD}
```

### Configuração de Ambiente no Código

```csharp
// Program.cs
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

var builder = Host.CreateApplicationBuilder(args);

// Configurar fontes de configuração
builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables()
    .AddCommandLine(args);

// Carregar arquivo .env baseado no ambiente
var envFile = builder.Environment.IsDevelopment() ? ".env.development" : ".env.production";
if (File.Exists(envFile))
{
    DotNetEnv.Env.Load(envFile);
    builder.Configuration.AddEnvironmentVariables();
}
```

---

## Configurações de Build

### Build Scripts (build.ps1)

```powershell
#!/usr/bin/env pwsh

param(
    [string]$Configuration = "Release",
    [string]$Runtime = "",
    [switch]$SelfContained = $false,
    [switch]$SingleFile = $false,
    [switch]$Clean = $false,
    [switch]$Restore = $true,
    [switch]$Test = $true,
    [switch]$Pack = $false
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 Iniciando build do Auto-Instalador Desktop" -ForegroundColor Green
Write-Host "Configuração: $Configuration" -ForegroundColor Yellow

# Limpar se solicitado
if ($Clean) {
    Write-Host "🧹 Limpando artefatos anteriores..." -ForegroundColor Yellow
    dotnet clean --configuration $Configuration
    Remove-Item -Path "./artifacts" -Recurse -Force -ErrorAction SilentlyContinue
}

# Restaurar dependências
if ($Restore) {
    Write-Host "📦 Restaurando dependências..." -ForegroundColor Yellow
    dotnet restore
}

# Build
Write-Host "🔨 Compilando aplicação..." -ForegroundColor Yellow
$buildArgs = @(
    "build"
    "--configuration", $Configuration
    "--no-restore"
)

if ($Runtime) {
    $buildArgs += "--runtime", $Runtime
}

if ($SelfContained) {
    $buildArgs += "--self-contained"
}

dotnet @buildArgs

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Falha na compilação"
    exit $LASTEXITCODE
}

# Executar testes
if ($Test) {
    Write-Host "🧪 Executando testes..." -ForegroundColor Yellow
    dotnet test --configuration $Configuration --no-build --verbosity normal
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "❌ Falha nos testes"
        exit $LASTEXITCODE
    }
}

# Publicar
Write-Host "📦 Publicando aplicação..." -ForegroundColor Yellow
$publishArgs = @(
    "publish"
    "src/AutoInstaller.UI/AutoInstaller.UI.csproj"
    "--configuration", $Configuration
    "--no-build"
    "--output", "./artifacts/publish"
)

if ($Runtime) {
    $publishArgs += "--runtime", $Runtime
}

if ($SelfContained) {
    $publishArgs += "--self-contained"
}

if ($SingleFile) {
    $publishArgs += "-p:PublishSingleFile=true"
}

dotnet @publishArgs

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Falha na publicação"
    exit $LASTEXITCODE
}

# Empacotar se solicitado
if ($Pack) {
    Write-Host "📦 Criando pacotes..." -ForegroundColor Yellow
    dotnet pack --configuration $Configuration --no-build --output ./artifacts/packages
}

Write-Host "✅ Build concluído com sucesso!" -ForegroundColor Green
```

### GitHub Actions (.github/workflows/build.yml)

```yaml
name: Build and Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
        
    runs-on: ${{ matrix.os }}
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'
        
    - name: Restore dependencies
      run: dotnet restore
      
    - name: Build
      run: dotnet build --configuration Release --no-restore
      
    - name: Test
      run: dotnet test --configuration Release --no-build --verbosity normal --collect:"XPlat Code Coverage"
      
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      if: matrix.os == 'ubuntu-latest'
      
    - name: Publish (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        dotnet publish src/AutoInstaller.UI/AutoInstaller.UI.csproj -c Release -r win-x64 --self-contained -o ./artifacts/win-x64
        dotnet publish src/AutoInstaller.UI/AutoInstaller.UI.csproj -c Release -r win-x86 --self-contained -o ./artifacts/win-x86
        
    - name: Publish (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: |
        dotnet publish src/AutoInstaller.UI/AutoInstaller.UI.csproj -c Release -r linux-x64 --self-contained -o ./artifacts/linux-x64
        
    - name: Publish (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        dotnet publish src/AutoInstaller.UI/AutoInstaller.UI.csproj -c Release -r osx-x64 --self-contained -o ./artifacts/osx-x64
        dotnet publish src/AutoInstaller.UI/AutoInstaller.UI.csproj -c Release -r osx-arm64 --self-contained -o ./artifacts/osx-arm64
        
    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-${{ matrix.os }}
        path: ./artifacts/
```

---

## Configurações de Deploy

### Configuração de Release

```json
{
  "version": "1.0.0",
  "releaseNotes": "Versão inicial do Auto-Instalador Desktop",
  "platforms": {
    "windows": {
      "x64": {
        "runtime": "win-x64",
        "selfContained": true,
        "singleFile": true,
        "installer": "msi"
      },
      "x86": {
        "runtime": "win-x86",
        "selfContained": true,
        "singleFile": true,
        "installer": "msi"
      }
    },
    "linux": {
      "x64": {
        "runtime": "linux-x64",
        "selfContained": true,
        "singleFile": true,
        "installer": "deb"
      }
    },
    "macos": {
      "x64": {
        "runtime": "osx-x64",
        "selfContained": true,
        "singleFile": true,
        "installer": "pkg"
      },
      "arm64": {
        "runtime": "osx-arm64",
        "selfContained": true,
        "singleFile": true,
        "installer": "pkg"
      }
    }
  }
}
```

---

## Configurações de Testes

### xunit.runner.json

```json
{
  "$schema": "https://xunit.net/schema/current/xunit.runner.schema.json",
  "methodDisplay": "method",
  "methodDisplayOptions": "all",
  "preEnumerateTheories": false,
  "shadowCopy": false,
  "stopOnFail": false,
  "parallelizeAssembly": false,
  "parallelizeTestCollections": true,
  "maxParallelThreads": 0
}
```

### coverlet.runsettings

```xml
<?xml version="1.0" encoding="utf-8" ?>
<RunSettings>
  <DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="XPlat code coverage">
        <Configuration>
          <Format>opencover,cobertura,lcov,teamcity,html</Format>
          <Exclude>[*.Tests]*,[*.TestHelpers]*</Exclude>
          <ExcludeByAttribute>Obsolete,GeneratedCodeAttribute,CompilerGeneratedAttribute</ExcludeByAttribute>
          <ExcludeByFile>**/Migrations/**</ExcludeByFile>
          <IncludeDirectory>../src/</IncludeDirectory>
        </Configuration>
      </DataCollector>
    </DataCollectors>
  </DataCollectionRunSettings>
</RunSettings>
```

---

## Troubleshooting

### Problemas Comuns

#### 1. Erro de Certificado SSL
```bash
# Limpar certificados de desenvolvimento
dotnet dev-certs https --clean
dotnet dev-certs https --trust
```

#### 2. Problemas com Docker
```bash
# Verificar se o Docker está rodando
docker info

# Reiniciar Docker Desktop
# Windows: Restart Docker Desktop
# Linux: sudo systemctl restart docker
```



#### 4. Problemas de Build
```bash
# Limpar cache do NuGet
dotnet nuget locals all --clear

# Restaurar dependências
dotnet restore --force

# Rebuild completo
dotnet clean
dotnet build
```

#### 5. Problemas de Performance
```bash
# Verificar uso de memória
dotnet-counters monitor --process-id <PID>

# Profiling de performance
dotnet-trace collect --process-id <PID>
```

### Logs de Diagnóstico

#### Habilitar Logs Detalhados
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "AutoInstaller": "Debug"
    }
  }
}
```

#### Localização dos Logs
- **Windows**: `%APPDATA%/AutoInstaller/logs/`
- **macOS**: `~/Library/Application Support/AutoInstaller/logs/`
- **Linux**: `~/.local/share/AutoInstaller/logs/`

### Ferramentas de Diagnóstico

```bash
# Verificar versões instaladas
dotnet --info
docker --version
podman --version

# Verificar saúde do sistema
dotnet-counters ps
dotnet-dump ps

# Análise de memória
dotnet-gcdump collect -p <PID>
```

---

## Scripts de Automação

### setup-dev.ps1
```powershell
#!/usr/bin/env pwsh

Write-Host "🚀 Configurando ambiente de desenvolvimento" -ForegroundColor Green

# Verificar pré-requisitos
if (!(Get-Command dotnet -ErrorAction SilentlyContinue)) {
    Write-Error "❌ .NET SDK não encontrado. Instale o .NET 9 SDK."
    exit 1
}

if (!(Get-Command git -ErrorAction SilentlyContinue)) {
    Write-Error "❌ Git não encontrado. Instale o Git."
    exit 1
}

# Restaurar dependências
Write-Host "📦 Restaurando dependências..." -ForegroundColor Yellow
dotnet restore

# Configurar certificados de desenvolvimento
Write-Host "🔐 Configurando certificados..." -ForegroundColor Yellow
dotnet dev-certs https --trust

# Configurar banco de dados
Write-Host "🗄️ Configurando banco de dados..." -ForegroundColor Yellow
dotnet ef database update --project src/AutoInstaller.Infrastructure

# Executar testes
Write-Host "🧪 Executando testes..." -ForegroundColor Yellow
dotnet test

Write-Host "✅ Ambiente configurado com sucesso!" -ForegroundColor Green
Write-Host "Execute 'dotnet run --project src/AutoInstaller.UI' para iniciar a aplicação." -ForegroundColor Cyan
```

### clean-dev.ps1
```powershell
#!/usr/bin/env pwsh

Write-Host "🧹 Limpando ambiente de desenvolvimento" -ForegroundColor Yellow

# Limpar artefatos de build
Get-ChildItem -Path . -Recurse -Directory -Name "bin" | Remove-Item -Recurse -Force
Get-ChildItem -Path . -Recurse -Directory -Name "obj" | Remove-Item -Recurse -Force

# Limpar cache do NuGet
dotnet nuget locals all --clear

# Limpar logs
Remove-Item -Path "./logs" -Recurse -Force -ErrorAction SilentlyContinue

# Limpar banco de dados de desenvolvimento
Remove-Item -Path "./data" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "✅ Limpeza concluída!" -ForegroundColor Green
```

Este documento fornece todas as configurações necessárias para configurar e manter o ambiente de desenvolvimento e produção do Auto-Instalador Desktop, garantindo consistência e facilidade de manutenção em diferentes plataformas.