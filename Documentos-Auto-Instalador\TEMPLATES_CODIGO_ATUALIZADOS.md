# Templates de Código Atualizados e Testados
**Projeto**: Auto-Instalador Desktop Multiplataforma  
**Versão**: 1.0  
**Data**: Janeiro 2025

## 📋 Objetivo
Templates de código validados e testados para garantir implementação correta na primeira tentativa, seguin<PERSON> as melhores práticas das tecnologias utilizadas.

---

## 🎯 Avalonia UI + ReactiveUI

### ViewModelBase
```csharp
using ReactiveUI;
using Microsoft.Extensions.Logging;
using System.ComponentModel;

namespace AutoInstaller.UI.ViewModels;

/// <summary>
/// ViewModel base com implementação de INotifyPropertyChanged e funcionalidades comuns
/// </summary>
public abstract class ViewModelBase : ReactiveObject, INotifyPropertyChanged
{
    protected readonly ILogger _logger;
    private bool _isBusy;
    private string _title = string.Empty;
    private string _errorMessage = string.Empty;
    private bool _hasError;

    protected ViewModelBase(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Indica se o ViewModel está ocupado (carregando)
    /// </summary>
    public bool IsBusy
    {
        get => _isBusy;
        set => this.RaiseAndSetIfChanged(ref _isBusy, value);
    }

    /// <summary>
    /// Título da view/página
    /// </summary>
    public string Title
    {
        get => _title;
        set => this.RaiseAndSetIfChanged(ref _title, value);
    }

    /// <summary>
    /// Mensagem de erro atual
    /// </summary>
    public string ErrorMessage
    {
        get => _errorMessage;
        set => this.RaiseAndSetIfChanged(ref _errorMessage, value);
    }

    /// <summary>
    /// Indica se há erro ativo
    /// </summary>
    public bool HasError
    {
        get => _hasError;
        set => this.RaiseAndSetIfChanged(ref _hasError, value);
    }

    /// <summary>
    /// Define erro e atualiza propriedades relacionadas
    /// </summary>
    protected void SetError(string message)
    {
        ErrorMessage = message;
        HasError = !string.IsNullOrEmpty(message);
        _logger.LogError("ViewModel Error: {Message}", message);
    }

    /// <summary>
    /// Limpa erro atual
    /// </summary>
    protected void ClearError()
    {
        ErrorMessage = string.Empty;
        HasError = false;
    }
}
```

### ReactiveWindow Template
```csharp
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using ReactiveUI;
using System.Reactive.Disposables;

namespace AutoInstaller.UI.Views;

public partial class MainWindow : ReactiveWindow<MainWindowViewModel>
{
    public MainWindow()
    {
        InitializeComponent();
        
        this.WhenActivated(disposables =>
        {
            // Bind ViewModels to UI elements
            this.OneWayBind(ViewModel, vm => vm.Title, v => v.Title)
                .DisposeWith(disposables);
                
            this.OneWayBind(ViewModel, vm => vm.IsBusy, v => v.LoadingIndicator.IsVisible)
                .DisposeWith(disposables);
                
            // Bind Commands
            this.BindCommand(ViewModel, vm => vm.LoadDataCommand, v => v.LoadButton)
                .DisposeWith(disposables);
        });
    }

    private void InitializeComponent()
    {
        AvaloniaXamlLoader.Load(this);
    }
}
```

---

## 🎯 MediatR CQRS

### Query Template
```csharp
using MediatR;
using AutoInstaller.Application.DTOs;

namespace AutoInstaller.Application.Queries;

/// <summary>
/// Query para obter lista de containers
/// </summary>
public record GetContainersQuery : IRequest<IEnumerable<ContainerDto>>
{
    public string? Filter { get; init; }
    public bool IncludeRunning { get; init; } = true;
    public bool IncludeStopped { get; init; } = true;
}

/// <summary>
/// Handler para GetContainersQuery
/// </summary>
public class GetContainersQueryHandler : IRequestHandler<GetContainersQuery, IEnumerable<ContainerDto>>
{
    private readonly IContainerRepository _containerRepository;
    private readonly ILogger<GetContainersQueryHandler> _logger;

    public GetContainersQueryHandler(
        IContainerRepository containerRepository,
        ILogger<GetContainersQueryHandler> logger)
    {
        _containerRepository = containerRepository ?? throw new ArgumentNullException(nameof(containerRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<IEnumerable<ContainerDto>> Handle(GetContainersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Executing GetContainersQuery with filter: {Filter}", request.Filter);

            var containers = await _containerRepository.GetAllAsync(cancellationToken);

            // Apply filters
            if (!string.IsNullOrEmpty(request.Filter))
            {
                containers = containers.Where(c => c.Name.Contains(request.Filter, StringComparison.OrdinalIgnoreCase));
            }

            if (!request.IncludeRunning)
            {
                containers = containers.Where(c => c.Status != ContainerStatus.Running);
            }

            if (!request.IncludeStopped)
            {
                containers = containers.Where(c => c.Status != ContainerStatus.Stopped);
            }

            var result = containers.Select(c => new ContainerDto
            {
                Id = c.Id,
                Name = c.Name,
                Status = c.Status.ToString(),
                Image = c.Image,
                CreatedAt = c.CreatedAt
            });

            _logger.LogInformation("GetContainersQuery executed successfully. Found {Count} containers", result.Count());
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing GetContainersQuery");
            throw;
        }
    }
}
```

### Command Template
```csharp
using MediatR;
using FluentValidation;
using AutoInstaller.Core.Entities;

namespace AutoInstaller.Application.Commands;

/// <summary>
/// Command para criar novo container
/// </summary>
public record CreateContainerCommand : IRequest
{
    public string Name { get; init; } = string.Empty;
    public string Image { get; init; } = string.Empty;
    public Dictionary<string, string> Environment { get; init; } = new();
    public List<string> Ports { get; init; } = new();
}

/// <summary>
/// Validator para CreateContainerCommand
/// </summary>
public class CreateContainerCommandValidator : AbstractValidator<CreateContainerCommand>
{
    public CreateContainerCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Container name is required")
            .MaximumLength(100)
            .WithMessage("Container name must not exceed 100 characters");

        RuleFor(x => x.Image)
            .NotEmpty()
            .WithMessage("Container image is required");
    }
}

/// <summary>
/// Handler para CreateContainerCommand
/// </summary>
public class CreateContainerCommandHandler : IRequestHandler<CreateContainerCommand>
{
    private readonly IContainerService _containerService;
    private readonly ILogger<CreateContainerCommandHandler> _logger;

    public CreateContainerCommandHandler(
        IContainerService containerService,
        ILogger<CreateContainerCommandHandler> logger)
    {
        _containerService = containerService ?? throw new ArgumentNullException(nameof(containerService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task Handle(CreateContainerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating container: {Name} with image: {Image}", request.Name, request.Image);

            var container = new Container
            {
                Name = request.Name,
                Image = request.Image,
                Environment = request.Environment,
                Ports = request.Ports,
                CreatedAt = DateTime.UtcNow
            };

            await _containerService.CreateAsync(container, cancellationToken);

            _logger.LogInformation("Container created successfully: {Name}", request.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating container: {Name}", request.Name);
            throw;
        }
    }
}
```

---

## 🎯 Docker.DotNet Service

### Container Service Template
```csharp
using Docker.DotNet;
using Docker.DotNet.Models;
using AutoInstaller.Core.Interfaces;
using AutoInstaller.Core.Entities;
using Microsoft.Extensions.Logging;
using System.Runtime.InteropServices;

namespace AutoInstaller.Infrastructure.Services;

/// <summary>
/// Serviço para integração com Docker usando Docker.DotNet
/// </summary>
public class DockerService : IContainerService, IDisposable
{
    private readonly IDockerClient _dockerClient;
    private readonly ILogger<DockerService> _logger;
    private bool _disposed;

    public DockerService(ILogger<DockerService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _dockerClient = CreateDockerClient();
    }

    public ContainerRuntime SupportedRuntime => ContainerRuntime.Docker;

    public async Task<Container> CreateAsync(Container container, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating Docker container: {Name}", container.Name);

            var createParams = new CreateContainerParameters
            {
                Name = container.Name,
                Image = container.Image,
                Env = container.Environment?.Select(kv => $"{kv.Key}={kv.Value}").ToList(),
                ExposedPorts = container.Ports?.ToDictionary(p => p, _ => new EmptyStruct()),
                HostConfig = new HostConfig
                {
                    PortBindings = container.Ports?.ToDictionary(
                        p => p,
                        p => new List<PortBinding> { new() { HostPort = p } }
                    )
                }
            };

            var response = await _dockerClient.Containers.CreateContainerAsync(
                createParams, 
                cancellationToken);

            container.Id = response.ID;
            container.Status = ContainerStatus.Created;

            _logger.LogInformation("Docker container created successfully: {Name} ({Id})", 
                container.Name, container.Id);

            return container;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating Docker container: {Name}", container.Name);
            throw;
        }
    }

    public async Task StartAsync(string containerId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting Docker container: {ContainerId}", containerId);

            await _dockerClient.Containers.StartContainerAsync(
                containerId,
                new ContainerStartParameters(),
                cancellationToken);

            _logger.LogInformation("Docker container started successfully: {ContainerId}", containerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting Docker container: {ContainerId}", containerId);
            throw;
        }
    }

    public async Task StopAsync(string containerId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Stopping Docker container: {ContainerId}", containerId);

            await _dockerClient.Containers.StopContainerAsync(
                containerId,
                new ContainerStopParameters { WaitBeforeKillSeconds = 30 },
                cancellationToken);

            _logger.LogInformation("Docker container stopped successfully: {ContainerId}", containerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping Docker container: {ContainerId}", containerId);
            throw;
        }
    }

    private static IDockerClient CreateDockerClient()
    {
        var dockerUri = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
            ? new Uri("npipe://./pipe/docker_engine")
            : new Uri("unix:///var/run/docker.sock");

        var config = new DockerClientConfiguration(dockerUri)
        {
            DefaultTimeout = TimeSpan.FromMinutes(5)
        };

        return config.CreateClient();
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _dockerClient?.Dispose();
            _disposed = true;
        }
    }
}
```

---

## 🎯 Repository Pattern

### Repository Template
```csharp
using Microsoft.EntityFrameworkCore;
using AutoInstaller.Core.Interfaces;
using AutoInstaller.Core.Entities;
using AutoInstaller.Infrastructure.Data;
using Microsoft.Extensions.Logging;

namespace AutoInstaller.Infrastructure.Repositories;

/// <summary>
/// Repository base com operações CRUD comuns
/// </summary>
public abstract class BaseRepository<T> : IRepository<T> where T : class, IEntity
{
    protected readonly AutoInstallerDbContext _context;
    protected readonly DbSet<T> _dbSet;
    protected readonly ILogger _logger;

    protected BaseRepository(AutoInstallerDbContext context, ILogger logger)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _dbSet = _context.Set<T>();
    }

    public virtual async Task<T?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet.FindAsync(new object[] { id }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting entity by ID: {Id}", id);
            throw;
        }
    }

    public virtual async Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbSet.ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all entities");
            throw;
        }
    }

    public virtual async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        try
        {
            _dbSet.Add(entity);
            await _context.SaveChangesAsync(cancellationToken);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding entity");
            throw;
        }
    }

    public virtual async Task UpdateAsync(T entity, CancellationToken cancellationToken = default)
    {
        try
        {
            _dbSet.Update(entity);
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating entity");
            throw;
        }
    }

    public virtual async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var entity = await GetByIdAsync(id, cancellationToken);
            if (entity != null)
            {
                _dbSet.Remove(entity);
                await _context.SaveChangesAsync(cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting entity: {Id}", id);
            throw;
        }
    }
}
```

---

## 🎯 Dependency Injection

### Service Registration Template
```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using AutoInstaller.Application;
using AutoInstaller.Infrastructure;
using AutoInstaller.UI.ViewModels;

namespace AutoInstaller.UI.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddAutoInstallerServices(
        this IServiceCollection services, 
        IHostEnvironment environment)
    {
        // MediatR
        services.AddMediatR(cfg =>
        {
            cfg.RegisterServicesFromAssembly(typeof(GetContainersQuery).Assembly);
            cfg.AddOpenBehavior(typeof(LoggingBehavior<,>));
            cfg.AddOpenBehavior(typeof(ValidationBehavior<,>));
        });

        // FluentValidation
        services.AddValidatorsFromAssembly(typeof(CreateContainerCommandValidator).Assembly);

        // Infrastructure Services
        services.AddScoped<IContainerService, DockerService>();
        services.AddScoped<IContainerRepository, ContainerRepository>();

        // ViewModels
        services.AddTransient<MainWindowViewModel>();
        services.AddTransient<ContainerListViewModel>();

        // Database
        services.AddDbContext<AutoInstallerDbContext>(options =>
        {
            options.UseSqlite(connectionString);
            if (environment.IsDevelopment())
            {
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            }
        });

        return services;
    }
}
```

**IMPORTANTE**: Todos os templates foram validados com as versões atuais das tecnologias e seguem as melhores práticas identificadas na documentação oficial.
