using AutoInstaller.Application.Commands;
using AutoInstaller.Core.Entities;
using AutoInstaller.Core.Enums;
using AutoInstaller.Core.Exceptions;
using AutoInstaller.Core.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace AutoInstaller.Application.Handlers;

/// <summary>
/// Handler para comando de iniciar engine embarcada
/// Implementa padrão CQRS com handlers void retornando Task
/// </summary>
public class StartEmbeddedEngineHandler : IRequestHandler<StartEmbeddedEngineCommand, StartEmbeddedEngineResult>
{
    private readonly IEmbeddedEngineRepository _repository;
    private readonly IEngineProcessManager _processManager;
    private readonly IEngineExtractor _extractor;
    private readonly IEngineConfigurator _configurator;
    private readonly ILogger<StartEmbeddedEngineHandler> _logger;

    public StartEmbeddedEngineHandler(
        IEmbeddedEngineRepository repository,
        IEngineProcessManager processManager,
        IEngineExtractor extractor,
        IEngineConfigurator configurator,
        ILogger<StartEmbeddedEngineHandler> logger)
    {
        _repository = repository;
        _processManager = processManager;
        _extractor = extractor;
        _configurator = configurator;
        _logger = logger;
    }

    public async Task<StartEmbeddedEngineResult> Handle(
        StartEmbeddedEngineCommand request, 
        CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Iniciando engine embarcada: {EngineName} ({EngineType})", 
                request.EngineName, request.EngineType);

            // 1. Verificar se engine já existe
            var existingEngine = await _repository.GetByNameAsync(request.EngineName, cancellationToken);
            
            if (existingEngine != null)
            {
                return await HandleExistingEngine(existingEngine, request, stopwatch, cancellationToken);
            }

            // 2. Criar nova engine
            var engine = EmbeddedEngine.Create(
                request.EngineName,
                request.EngineType,
                request.BinaryPath,
                request.Version,
                request.OperatingSystem,
                request.Architecture);

            // 3. Extrair binários se necessário
            if (!engine.IsExtracted)
            {
                _logger.LogInformation("Extraindo binários da engine {EngineName}", request.EngineName);
                
                var extractedPath = await _extractor.ExtractEngineAsync(
                    request.EngineType,
                    request.BinaryPath,
                    request.OperatingSystem,
                    request.Architecture,
                    cancellationToken);

                engine.MarkAsExtracted(extractedPath);
            }

            // 4. Configurar engine
            if (!engine.IsConfigured)
            {
                _logger.LogInformation("Configurando engine {EngineName}", request.EngineName);
                
                var configuration = await _configurator.ConfigureEngineAsync(
                    engine,
                    cancellationToken);

                var communicationEndpoint = await _configurator.GetCommunicationEndpointAsync(
                    engine,
                    cancellationToken);

                engine.MarkAsConfigured(configuration, communicationEndpoint);
            }

            // 5. Salvar engine no repositório
            await _repository.AddAsync(engine, cancellationToken);

            // 6. Iniciar processo da engine
            _logger.LogInformation("Iniciando processo da engine {EngineName}", request.EngineName);
            
            var processId = await _processManager.StartEngineAsync(
                engine,
                request.TimeoutSeconds,
                cancellationToken);

            engine.Start(processId);

            // 7. Aguardar engine ficar disponível
            var isRunning = await _processManager.WaitForEngineReadyAsync(
                engine,
                TimeSpan.FromSeconds(request.TimeoutSeconds),
                cancellationToken);

            if (isRunning)
            {
                engine.MarkAsRunning();
                await _repository.UpdateAsync(engine, cancellationToken);

                stopwatch.Stop();
                
                _logger.LogInformation("Engine {EngineName} iniciada com sucesso em {ElapsedSeconds:F2}s", 
                    request.EngineName, stopwatch.Elapsed.TotalSeconds);

                return StartEmbeddedEngineResult.CreateSuccess(
                    engine.Id.ToString(),
                    $"Engine {request.EngineName} iniciada com sucesso",
                    engine.CommunicationEndpoint,
                    processId,
                    stopwatch.Elapsed.TotalSeconds);
            }
            else
            {
                engine.MarkAsError("Engine não ficou disponível no tempo esperado");
                await _repository.UpdateAsync(engine, cancellationToken);

                return StartEmbeddedEngineResult.CreateFailure(
                    $"Engine {request.EngineName} não ficou disponível no tempo esperado",
                    new List<string> { "Timeout aguardando engine ficar disponível" });
            }
        }
        catch (DomainException ex)
        {
            _logger.LogWarning(ex, "Erro de domínio ao iniciar engine {EngineName}: {Message}", 
                request.EngineName, ex.Message);

            return StartEmbeddedEngineResult.CreateFailure(
                $"Erro ao iniciar engine {request.EngineName}: {ex.Message}",
                new List<string> { ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro inesperado ao iniciar engine {EngineName}", request.EngineName);

            return StartEmbeddedEngineResult.CreateFailure(
                $"Erro interno ao iniciar engine {request.EngineName}",
                new List<string> { ex.Message });
        }
    }

    private async Task<StartEmbeddedEngineResult> HandleExistingEngine(
        EmbeddedEngine engine,
        StartEmbeddedEngineCommand request,
        Stopwatch stopwatch,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Engine {EngineName} já existe com status {Status}", 
            engine.Name, engine.Status);

        // Se já está rodando e não é para forçar restart
        if (engine.Status == EngineStatus.Running && !request.ForceRestart)
        {
            stopwatch.Stop();
            
            return StartEmbeddedEngineResult.CreateSuccess(
                engine.Id.ToString(),
                $"Engine {engine.Name} já está em execução",
                engine.CommunicationEndpoint,
                engine.ProcessId,
                stopwatch.Elapsed.TotalSeconds);
        }

        // Se é para forçar restart ou engine não está rodando
        if (request.ForceRestart && engine.Status == EngineStatus.Running)
        {
            _logger.LogInformation("Forçando restart da engine {EngineName}", engine.Name);
            
            await _processManager.StopEngineAsync(engine, 30, cancellationToken);
            engine.Stop();
            engine.MarkAsStopped();
        }

        // Verificar se pode iniciar
        if (!engine.CanStart())
        {
            return StartEmbeddedEngineResult.CreateFailure(
                $"Engine {engine.Name} não pode ser iniciada no status atual: {engine.Status}",
                new List<string> { $"Status atual: {engine.Status}" });
        }

        // Iniciar engine existente
        var processId = await _processManager.StartEngineAsync(
            engine,
            request.TimeoutSeconds,
            cancellationToken);

        engine.Start(processId);

        var isRunning = await _processManager.WaitForEngineReadyAsync(
            engine,
            TimeSpan.FromSeconds(request.TimeoutSeconds),
            cancellationToken);

        if (isRunning)
        {
            engine.MarkAsRunning();
            await _repository.UpdateAsync(engine, cancellationToken);

            stopwatch.Stop();
            
            _logger.LogInformation("Engine existente {EngineName} iniciada com sucesso em {ElapsedSeconds:F2}s", 
                engine.Name, stopwatch.Elapsed.TotalSeconds);

            return StartEmbeddedEngineResult.CreateSuccess(
                engine.Id.ToString(),
                $"Engine {engine.Name} iniciada com sucesso",
                engine.CommunicationEndpoint,
                processId,
                stopwatch.Elapsed.TotalSeconds);
        }
        else
        {
            engine.MarkAsError("Engine não ficou disponível no tempo esperado");
            await _repository.UpdateAsync(engine, cancellationToken);

            return StartEmbeddedEngineResult.CreateFailure(
                $"Engine {engine.Name} não ficou disponível no tempo esperado",
                new List<string> { "Timeout aguardando engine ficar disponível" });
        }
    }
}
