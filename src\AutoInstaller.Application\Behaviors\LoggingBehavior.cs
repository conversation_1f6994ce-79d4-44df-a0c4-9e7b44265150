using MediatR;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text.Json;

namespace AutoInstaller.Application.Behaviors;

/// <summary>
/// Pipeline behavior para logging de requests/responses
/// Implementa cross-cutting concern para todas as operações CQRS
/// </summary>
public class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly ILogger<LoggingBehavior<TRequest, TResponse>> _logger;

    public LoggingBehavior(ILogger<LoggingBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    public async Task<TResponse> Handle(
        TRequest request, 
        RequestHandlerDelegate<TResponse> next, 
        CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var requestId = Guid.NewGuid();
        
        // Log início da operação
        _logger.LogInformation("Iniciando {RequestName} [ID: {RequestId}]: {@Request}", 
            requestName, requestId, request);
        
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var response = await next();
            
            stopwatch.Stop();
            
            // Log sucesso
            _logger.LogInformation("Concluído {RequestName} [ID: {RequestId}] em {ElapsedMs}ms: {@Response}", 
                requestName, requestId, stopwatch.ElapsedMilliseconds, response);
            
            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            // Log erro
            _logger.LogError(ex, "Erro em {RequestName} [ID: {RequestId}] após {ElapsedMs}ms: {ErrorMessage}", 
                requestName, requestId, stopwatch.ElapsedMilliseconds, ex.Message);
            
            throw;
        }
    }
}

/// <summary>
/// Pipeline behavior para validação usando FluentValidation
/// </summary>
public class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly IEnumerable<IValidator<TRequest>> _validators;
    private readonly ILogger<ValidationBehavior<TRequest, TResponse>> _logger;

    public ValidationBehavior(
        IEnumerable<IValidator<TRequest>> validators,
        ILogger<ValidationBehavior<TRequest, TResponse>> logger)
    {
        _validators = validators;
        _logger = logger;
    }

    public async Task<TResponse> Handle(
        TRequest request, 
        RequestHandlerDelegate<TResponse> next, 
        CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        
        if (!_validators.Any())
        {
            return await next();
        }

        _logger.LogDebug("Validando {RequestName}", requestName);

        var context = new ValidationContext<TRequest>(request);
        
        var validationResults = await Task.WhenAll(
            _validators.Select(v => v.ValidateAsync(context, cancellationToken)));

        var failures = validationResults
            .SelectMany(r => r.Errors)
            .Where(f => f != null)
            .ToList();

        if (failures.Any())
        {
            var errorMessage = string.Join("; ", failures.Select(f => f.ErrorMessage));
            
            _logger.LogWarning("Falha na validação de {RequestName}: {ValidationErrors}", 
                requestName, errorMessage);
            
            throw new ValidationException($"Erro de validação em {requestName}", failures);
        }

        _logger.LogDebug("Validação de {RequestName} concluída com sucesso", requestName);
        
        return await next();
    }
}

/// <summary>
/// Pipeline behavior para monitoramento de performance
/// </summary>
public class PerformanceBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly ILogger<PerformanceBehavior<TRequest, TResponse>> _logger;
    private readonly int _slowRequestThresholdMs;

    public PerformanceBehavior(
        ILogger<PerformanceBehavior<TRequest, TResponse>> logger,
        int slowRequestThresholdMs = 5000) // 5 segundos por padrão
    {
        _logger = logger;
        _slowRequestThresholdMs = slowRequestThresholdMs;
    }

    public async Task<TResponse> Handle(
        TRequest request, 
        RequestHandlerDelegate<TResponse> next, 
        CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var response = await next();
            
            stopwatch.Stop();
            
            if (stopwatch.ElapsedMilliseconds > _slowRequestThresholdMs)
            {
                _logger.LogWarning("Requisição lenta detectada: {RequestName} levou {ElapsedMs}ms para completar. Request: {@Request}", 
                    requestName, stopwatch.ElapsedMilliseconds, request);
            }
            else
            {
                _logger.LogDebug("Performance {RequestName}: {ElapsedMs}ms", 
                    requestName, stopwatch.ElapsedMilliseconds);
            }
            
            return response;
        }
        catch (Exception)
        {
            stopwatch.Stop();
            
            _logger.LogWarning("Requisição {RequestName} falhou após {ElapsedMs}ms", 
                requestName, stopwatch.ElapsedMilliseconds);
            
            throw;
        }
    }
}

/// <summary>
/// Pipeline behavior para retry automático
/// </summary>
public class RetryBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly ILogger<RetryBehavior<TRequest, TResponse>> _logger;
    private readonly int _maxRetries;
    private readonly TimeSpan _baseDelay;

    public RetryBehavior(
        ILogger<RetryBehavior<TRequest, TResponse>> logger,
        int maxRetries = 3,
        int baseDelayMs = 1000)
    {
        _logger = logger;
        _maxRetries = maxRetries;
        _baseDelay = TimeSpan.FromMilliseconds(baseDelayMs);
    }

    public async Task<TResponse> Handle(
        TRequest request, 
        RequestHandlerDelegate<TResponse> next, 
        CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        
        // Só aplicar retry para commands específicos (não queries)
        if (!ShouldRetry(request))
        {
            return await next();
        }

        Exception? lastException = null;
        
        for (int attempt = 1; attempt <= _maxRetries + 1; attempt++)
        {
            try
            {
                if (attempt > 1)
                {
                    var delay = TimeSpan.FromMilliseconds(_baseDelay.TotalMilliseconds * Math.Pow(2, attempt - 2));
                    
                    _logger.LogInformation("Tentativa {Attempt} de {MaxAttempts} para {RequestName} após delay de {DelayMs}ms", 
                        attempt, _maxRetries + 1, requestName, delay.TotalMilliseconds);
                    
                    await Task.Delay(delay, cancellationToken);
                }
                
                return await next();
            }
            catch (Exception ex) when (attempt <= _maxRetries && IsRetriableException(ex))
            {
                lastException = ex;
                
                _logger.LogWarning(ex, "Tentativa {Attempt} de {RequestName} falhou com erro retriável: {ErrorMessage}", 
                    attempt, requestName, ex.Message);
            }
        }
        
        _logger.LogError(lastException, "Todas as {MaxAttempts} tentativas de {RequestName} falharam", 
            _maxRetries + 1, requestName);
        
        throw lastException!;
    }

    private static bool ShouldRetry(TRequest request)
    {
        // Aplicar retry apenas para commands específicos relacionados a engines
        var requestType = typeof(TRequest);
        return requestType.Name.Contains("Engine") && 
               (requestType.Name.Contains("Start") || requestType.Name.Contains("Stop"));
    }

    private static bool IsRetriableException(Exception ex)
    {
        // Definir quais exceções são retriáveis
        return ex is TimeoutException ||
               ex is TaskCanceledException ||
               (ex is InvalidOperationException && ex.Message.Contains("process")) ||
               (ex.InnerException != null && IsRetriableException(ex.InnerException));
    }
}
