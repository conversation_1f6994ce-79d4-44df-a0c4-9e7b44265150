namespace AutoInstaller.Core.Entities;

/// <summary>
/// Classe base para todas as entidades de domínio
/// Implementa padrões DDD com identidade única e auditoria
/// </summary>
public abstract class Entity : IEquatable<Entity>
{
    /// <summary>
    /// Identificador único da entidade
    /// </summary>
    public Guid Id { get; protected set; }

    /// <summary>
    /// Data de criação da entidade
    /// </summary>
    public DateTime CreatedAt { get; protected set; }

    /// <summary>
    /// Data da última atualização
    /// </summary>
    public DateTime UpdatedAt { get; protected set; }

    /// <summary>
    /// Usuário que criou a entidade
    /// </summary>
    public string CreatedBy { get; protected set; }

    /// <summary>
    /// Usuário que fez a última atualização
    /// </summary>
    public string UpdatedBy { get; protected set; }

    /// <summary>
    /// Versão para controle de concorrência otimista
    /// </summary>
    public long Version { get; protected set; }

    /// <summary>
    /// Construtor protegido para Entity Framework
    /// </summary>
    protected Entity()
    {
        Id = Guid.NewGuid();
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
        CreatedBy = "System";
        UpdatedBy = "System";
        Version = 1;
    }

    /// <summary>
    /// Construtor com identificador específico
    /// </summary>
    protected Entity(Guid id) : this()
    {
        if (id == Guid.Empty)
            throw new ArgumentException("Id não pode ser vazio", nameof(id));

        Id = id;
    }

    /// <summary>
    /// Atualiza informações de auditoria
    /// </summary>
    public virtual void UpdateAuditInfo(string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(updatedBy))
            throw new ArgumentException("UpdatedBy não pode ser vazio", nameof(updatedBy));

        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy;
        Version++;
    }

    /// <summary>
    /// Define informações de criação
    /// </summary>
    public virtual void SetCreationInfo(string createdBy)
    {
        if (string.IsNullOrWhiteSpace(createdBy))
            throw new ArgumentException("CreatedBy não pode ser vazio", nameof(createdBy));

        CreatedBy = createdBy;
        UpdatedBy = createdBy;
    }

    /// <summary>
    /// Implementação de igualdade baseada no Id
    /// </summary>
    public bool Equals(Entity? other)
    {
        if (other is null) return false;
        if (ReferenceEquals(this, other)) return true;
        if (GetType() != other.GetType()) return false;
        
        return Id == other.Id;
    }

    /// <summary>
    /// Override do Equals
    /// </summary>
    public override bool Equals(object? obj)
    {
        return Equals(obj as Entity);
    }

    /// <summary>
    /// Override do GetHashCode
    /// </summary>
    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    /// <summary>
    /// Operador de igualdade
    /// </summary>
    public static bool operator ==(Entity? left, Entity? right)
    {
        return Equals(left, right);
    }

    /// <summary>
    /// Operador de desigualdade
    /// </summary>
    public static bool operator !=(Entity? left, Entity? right)
    {
        return !Equals(left, right);
    }

    /// <summary>
    /// Override do ToString
    /// </summary>
    public override string ToString()
    {
        return $"{GetType().Name} [Id={Id}]";
    }
}
