# Checklist de Validação Pré-Implementação
**Projeto**: Auto-Instalador Desktop Multiplataforma  
**Versão**: 1.0  
**Data**: Janeiro 2025

## 📋 Objetivo
Este checklist deve ser seguido **OBRIGATORIAMENTE** antes de qualquer implementação para garantir desenvolvimento assertivo e reduzir erros sistemáticos.

---

## ✅ 1. Validação de Tecnologias e Versões

### .NET 9.0
- [ ] Confirmar uso de .NET 9.0 (`<TargetFramework>net9.0</TargetFramework>`)
- [ ] Verificar APIs não obsoletas na documentação oficial
- [ ] Confirmar compatibilidade com outras dependências
- [ ] Validar breaking changes do .NET 9

### Avalonia UI 11.3.4
- [ ] Confirmar versão exata: `11.3.4`
- [ ] Verificar padrões MVVM com ReactiveUI
- [ ] Validar uso de `ReactiveObject` como base para ViewModels
- [ ] Confirmar uso de `RaiseAndSetIfChanged` para propriedades
- [ ] Verificar `ReactiveCommand` para comandos
- [ ] Validar `WhenActivated` para subscriptions
- [ ] Confirmar `UseReactiveUI()` no AppBuilder

### Docker.DotNet 3.125.15
- [ ] Confirmar versão exata: `3.125.15`
- [ ] **NUNCA** usar Docker CLI - apenas Docker.DotNet
- [ ] Verificar padrões async/await
- [ ] Validar tratamento de exceções
- [ ] Confirmar dispose de recursos

### MediatR (Versão Atual)
- [ ] Usar versão consistente em todo o projeto
- [ ] Para requests com resposta: `IRequestHandler<TRequest, TResponse>`
- [ ] Para requests void: `IRequestHandler<TRequest>` (retorna `Task`)
- [ ] Para notifications: `INotificationHandler<TNotification>`
- [ ] Para behaviors: `IPipelineBehavior<TRequest, TResponse>`
- [ ] Registro: `services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(...))`
- [ ] **NÃO** usar `Task<Unit>` - usar `Task` para void handlers

---

## ✅ 2. Validação Arquitetural

### Clean Architecture
- [ ] Confirmar separação de camadas (Core, Application, Infrastructure, UI)
- [ ] Validar dependências unidirecionais
- [ ] Verificar interfaces no Core
- [ ] Confirmar implementações na Infrastructure

### CQRS com MediatR
- [ ] Commands para operações de escrita
- [ ] Queries para operações de leitura
- [ ] Handlers separados para cada operação
- [ ] Validação com FluentValidation

### Padrão Repository
- [ ] Interfaces no Core
- [ ] Implementações na Infrastructure
- [ ] Uso de Entity Framework Core
- [ ] Padrões async/await

---

## ✅ 3. Validação de Implementação

### ViewModels (Avalonia UI)
```csharp
// ✅ CORRETO
public class MyViewModel : ViewModelBase
{
    private string _property;
    public string Property
    {
        get => _property;
        set => this.RaiseAndSetIfChanged(ref _property, value);
    }
    
    public ReactiveCommand<Unit, Unit> MyCommand { get; }
    
    public MyViewModel()
    {
        MyCommand = ReactiveCommand.Create(ExecuteCommand);
    }
}
```

### MediatR Handlers
```csharp
// ✅ CORRETO - Request com resposta
public class GetDataHandler : IRequestHandler<GetDataQuery, DataDto>
{
    public async Task<DataDto> Handle(GetDataQuery request, CancellationToken cancellationToken)
    {
        // Implementação
    }
}

// ✅ CORRETO - Request void
public class CreateDataHandler : IRequestHandler<CreateDataCommand>
{
    public async Task Handle(CreateDataCommand request, CancellationToken cancellationToken)
    {
        // Implementação
        return; // NÃO usar Unit.Value
    }
}
```

### Docker.DotNet
```csharp
// ✅ CORRETO
public class DockerService : IContainerService, IDisposable
{
    private readonly IDockerClient _dockerClient;
    
    public async Task<Container> CreateContainerAsync(CreateContainerRequest request)
    {
        var response = await _dockerClient.Containers.CreateContainerAsync(
            new CreateContainerParameters(), 
            cancellationToken);
        return response;
    }
}
```

---

## ✅ 4. Validação de Qualidade

### Testes
- [ ] Testes unitários implementados
- [ ] Cobertura mínima de 80%
- [ ] Testes de integração quando necessário
- [ ] Mocks apropriados com Moq

### Logging
- [ ] Uso de Serilog estruturado
- [ ] Níveis de log apropriados
- [ ] Não exposição de dados sensíveis

### Tratamento de Erros
- [ ] Try-catch apropriados
- [ ] Exceções customizadas quando necessário
- [ ] Logging de erros
- [ ] Retorno de resultados apropriados

---

## ✅ 5. Validação de Configuração

### Dependências
- [ ] Versões consistentes em Directory.Build.props
- [ ] Compatibilidade entre dependências verificada
- [ ] Não há dependências obsoletas

### Configuração de Build
- [ ] `TreatWarningsAsErrors=true` configurado apropriadamente
- [ ] Warnings específicos excluídos quando necessário
- [ ] Análise de código habilitada

---

## ✅ 6. Checklist Final

- [ ] Código compila sem warnings
- [ ] Testes passam
- [ ] Documentação atualizada
- [ ] Padrões de código seguidos
- [ ] Performance validada
- [ ] Segurança verificada

---

## 🚨 Critérios de Bloqueio

**NÃO PROSSEGUIR** se algum item crítico não for atendido:

1. ❌ Uso de APIs obsoletas
2. ❌ Versões inconsistentes de dependências
3. ❌ Violação de padrões arquiteturais
4. ❌ Falta de testes para código crítico
5. ❌ Uso de Docker CLI em vez de Docker.DotNet
6. ❌ Padrões incorretos de MediatR
7. ❌ ViewModels não seguindo ReactiveUI

---

## 📚 Recursos de Consulta Obrigatória

- [Documentação .NET 9](https://docs.microsoft.com/dotnet/)
- [Documentação Avalonia UI](https://docs.avaloniaui.net/)
- [Documentação MediatR](https://github.com/jbogard/MediatR)
- [Documentação Docker.DotNet](https://github.com/dotnet/Docker.DotNet)
- [Documentação ReactiveUI](https://reactiveui.net/)

**IMPORTANTE**: Consultar documentação oficial ANTES de implementar qualquer funcionalidade.
