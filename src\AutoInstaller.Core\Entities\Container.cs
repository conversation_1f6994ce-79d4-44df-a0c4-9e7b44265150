using AutoInstaller.Core.Enums;
using AutoInstaller.Core.Events;
using AutoInstaller.Core.Exceptions;
using AutoInstaller.Core.ValueObjects;

namespace AutoInstaller.Core.Entities;

/// <summary>
/// Entidade de domínio para containers
/// Representa um container gerenciado por engines embarcadas
/// </summary>
public class Container : Entity
{
    private readonly List<DomainEvent> _domainEvents = new();
    private readonly List<PortMapping> _ports = new();
    private readonly List<VolumeMount> _volumes = new();
    private readonly List<EnvironmentVariable> _environment = new();

    /// <summary>
    /// Nome do container
    /// </summary>
    public string Name { get; private set; }

    /// <summary>
    /// ID do container na engine (Docker/Podman)
    /// </summary>
    public string? EngineContainerId { get; private set; }

    /// <summary>
    /// Imagem base do container
    /// </summary>
    public string ImageName { get; private set; }

    /// <summary>
    /// Tag da imagem
    /// </summary>
    public string ImageTag { get; private set; }

    /// <summary>
    /// Status atual do container
    /// </summary>
    public ContainerStatus Status { get; private set; }

    /// <summary>
    /// Engine que gerencia este container
    /// </summary>
    public Guid EmbeddedEngineId { get; private set; }

    /// <summary>
    /// Tipo da engine (Docker/Podman)
    /// </summary>
    public EngineType EngineType { get; private set; }

    /// <summary>
    /// Comando executado no container
    /// </summary>
    public string? Command { get; private set; }

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public string[]? Arguments { get; private set; }

    /// <summary>
    /// Diretório de trabalho
    /// </summary>
    public string? WorkingDirectory { get; private set; }

    /// <summary>
    /// Data de início do container
    /// </summary>
    public DateTime? StartedAt { get; private set; }

    /// <summary>
    /// Data de parada do container
    /// </summary>
    public DateTime? StoppedAt { get; private set; }

    /// <summary>
    /// Código de saída do container
    /// </summary>
    public int? ExitCode { get; private set; }

    /// <summary>
    /// Mensagem de erro se houver
    /// </summary>
    public string? ErrorMessage { get; private set; }

    /// <summary>
    /// Configurações de restart
    /// </summary>
    public RestartPolicy RestartPolicy { get; private set; }

    /// <summary>
    /// Configurações de recursos
    /// </summary>
    public ResourceLimits ResourceLimits { get; private set; }

    /// <summary>
    /// Mapeamentos de porta
    /// </summary>
    public IReadOnlyCollection<PortMapping> Ports => _ports.AsReadOnly();

    /// <summary>
    /// Montagens de volume
    /// </summary>
    public IReadOnlyCollection<VolumeMount> Volumes => _volumes.AsReadOnly();

    /// <summary>
    /// Variáveis de ambiente
    /// </summary>
    public IReadOnlyCollection<EnvironmentVariable> Environment => _environment.AsReadOnly();

    /// <summary>
    /// Construtor privado para Entity Framework
    /// </summary>
    private Container()
    {
        Name = string.Empty;
        ImageName = string.Empty;
        ImageTag = "latest";
        RestartPolicy = RestartPolicy.No;
        ResourceLimits = new ResourceLimits();
    }

    /// <summary>
    /// Construtor para criar novo container
    /// </summary>
    private Container(
        string name,
        string imageName,
        string imageTag,
        Guid embeddedEngineId,
        EngineType engineType) : this()
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new DomainException("Nome do container é obrigatório");

        if (string.IsNullOrWhiteSpace(imageName))
            throw new DomainException("Nome da imagem é obrigatório");

        if (embeddedEngineId == Guid.Empty)
            throw new DomainException("ID da engine embarcada é obrigatório");

        Name = name;
        ImageName = imageName;
        ImageTag = imageTag ?? "latest";
        EmbeddedEngineId = embeddedEngineId;
        EngineType = engineType;
        Status = ContainerStatus.Created;

        AddDomainEvent(new ContainerCreatedEvent(Id, Name, ImageName, ImageTag, engineType));
    }

    /// <summary>
    /// Factory method para criar novo container
    /// </summary>
    public static Container Create(
        string name,
        string imageName,
        string imageTag,
        Guid embeddedEngineId,
        EngineType engineType)
    {
        return new Container(name, imageName, imageTag, embeddedEngineId, engineType);
    }

    /// <summary>
    /// Define o ID do container na engine
    /// </summary>
    public void SetEngineContainerId(string engineContainerId)
    {
        if (string.IsNullOrWhiteSpace(engineContainerId))
            throw new DomainException("ID do container na engine é obrigatório");

        EngineContainerId = engineContainerId;
        AddDomainEvent(new ContainerEngineIdSetEvent(Id, Name, engineContainerId, EngineType));
    }

    /// <summary>
    /// Inicia o container
    /// </summary>
    public void Start()
    {
        if (Status == ContainerStatus.Running)
            throw new DomainException($"Container {Name} já está em execução");

        if (Status == ContainerStatus.Paused)
        {
            Resume();
            return;
        }

        Status = ContainerStatus.Starting;
        StartedAt = DateTime.UtcNow;
        StoppedAt = null;
        ExitCode = null;
        ErrorMessage = null;

        AddDomainEvent(new ContainerStartingEvent(Id, Name, EngineType));
    }

    /// <summary>
    /// Marca o container como em execução
    /// </summary>
    public void MarkAsRunning()
    {
        if (Status != ContainerStatus.Starting)
            throw new DomainException($"Container {Name} deve estar iniciando para ser marcado como em execução");

        Status = ContainerStatus.Running;
        AddDomainEvent(new ContainerRunningEvent(Id, Name, EngineType));
    }

    /// <summary>
    /// Para o container
    /// </summary>
    public void Stop()
    {
        if (Status != ContainerStatus.Running && Status != ContainerStatus.Paused)
            throw new DomainException($"Container {Name} não está em execução ou pausado");

        Status = ContainerStatus.Stopping;
        StoppedAt = DateTime.UtcNow;

        AddDomainEvent(new ContainerStoppingEvent(Id, Name, EngineType));
    }

    /// <summary>
    /// Marca o container como parado
    /// </summary>
    public void MarkAsStopped(int? exitCode = null)
    {
        if (Status != ContainerStatus.Stopping)
            throw new DomainException($"Container {Name} deve estar parando para ser marcado como parado");

        Status = ContainerStatus.Stopped;
        ExitCode = exitCode;
        AddDomainEvent(new ContainerStoppedEvent(Id, Name, EngineType, exitCode));
    }

    /// <summary>
    /// Pausa o container
    /// </summary>
    public void Pause()
    {
        if (Status != ContainerStatus.Running)
            throw new DomainException($"Container {Name} deve estar em execução para ser pausado");

        Status = ContainerStatus.Paused;
        AddDomainEvent(new ContainerPausedEvent(Id, Name, EngineType));
    }

    /// <summary>
    /// Resume o container pausado
    /// </summary>
    public void Resume()
    {
        if (Status != ContainerStatus.Paused)
            throw new DomainException($"Container {Name} deve estar pausado para ser resumido");

        Status = ContainerStatus.Running;
        AddDomainEvent(new ContainerResumedEvent(Id, Name, EngineType));
    }

    /// <summary>
    /// Marca o container com erro
    /// </summary>
    public void MarkAsError(string errorMessage)
    {
        if (string.IsNullOrWhiteSpace(errorMessage))
            throw new DomainException("Mensagem de erro é obrigatória");

        Status = ContainerStatus.Error;
        ErrorMessage = errorMessage;
        StoppedAt = DateTime.UtcNow;

        AddDomainEvent(new ContainerErrorEvent(Id, Name, EngineType, errorMessage));
    }

    /// <summary>
    /// Adiciona mapeamento de porta
    /// </summary>
    public void AddPortMapping(PortMapping portMapping)
    {
        if (portMapping == null)
            throw new DomainException("Mapeamento de porta é obrigatório");

        if (_ports.Any(p => p.HostPort == portMapping.HostPort))
            throw new DomainException($"Porta {portMapping.HostPort} já está mapeada");

        _ports.Add(portMapping);
    }

    /// <summary>
    /// Adiciona montagem de volume
    /// </summary>
    public void AddVolumeMount(VolumeMount volumeMount)
    {
        if (volumeMount == null)
            throw new DomainException("Montagem de volume é obrigatória");

        if (_volumes.Any(v => v.ContainerPath == volumeMount.ContainerPath))
            throw new DomainException($"Caminho {volumeMount.ContainerPath} já está montado");

        _volumes.Add(volumeMount);
    }

    /// <summary>
    /// Adiciona variável de ambiente
    /// </summary>
    public void AddEnvironmentVariable(EnvironmentVariable environmentVariable)
    {
        if (environmentVariable == null)
            throw new DomainException("Variável de ambiente é obrigatória");

        var existing = _environment.FirstOrDefault(e => e.Name == environmentVariable.Name);
        if (existing != null)
        {
            _environment.Remove(existing);
        }

        _environment.Add(environmentVariable);
    }

    /// <summary>
    /// Define comando e argumentos
    /// </summary>
    public void SetCommand(string command, string[]? arguments = null)
    {
        if (string.IsNullOrWhiteSpace(command))
            throw new DomainException("Comando é obrigatório");

        Command = command;
        Arguments = arguments;
    }

    /// <summary>
    /// Define diretório de trabalho
    /// </summary>
    public void SetWorkingDirectory(string workingDirectory)
    {
        if (string.IsNullOrWhiteSpace(workingDirectory))
            throw new DomainException("Diretório de trabalho é obrigatório");

        WorkingDirectory = workingDirectory;
    }

    /// <summary>
    /// Define política de restart
    /// </summary>
    public void SetRestartPolicy(RestartPolicy restartPolicy)
    {
        RestartPolicy = restartPolicy;
    }

    /// <summary>
    /// Define limites de recursos
    /// </summary>
    public void SetResourceLimits(ResourceLimits resourceLimits)
    {
        ResourceLimits = resourceLimits ?? throw new DomainException("Limites de recursos são obrigatórios");
    }

    /// <summary>
    /// Verifica se o container pode ser iniciado
    /// </summary>
    public bool CanStart()
    {
        return Status == ContainerStatus.Created || 
               Status == ContainerStatus.Stopped || 
               Status == ContainerStatus.Error ||
               Status == ContainerStatus.Paused;
    }

    /// <summary>
    /// Verifica se o container pode ser parado
    /// </summary>
    public bool CanStop()
    {
        return Status == ContainerStatus.Running || Status == ContainerStatus.Paused;
    }

    /// <summary>
    /// Verifica se o container pode ser pausado
    /// </summary>
    public bool CanPause()
    {
        return Status == ContainerStatus.Running;
    }

    /// <summary>
    /// Obtém eventos de domínio
    /// </summary>
    public IReadOnlyCollection<DomainEvent> GetDomainEvents() => _domainEvents.AsReadOnly();

    /// <summary>
    /// Limpa eventos de domínio
    /// </summary>
    public void ClearDomainEvents() => _domainEvents.Clear();

    /// <summary>
    /// Adiciona evento de domínio
    /// </summary>
    private void AddDomainEvent(DomainEvent domainEvent) => _domainEvents.Add(domainEvent);
}
