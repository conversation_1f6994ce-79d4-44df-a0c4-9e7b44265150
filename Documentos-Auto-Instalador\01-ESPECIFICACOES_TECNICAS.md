# Especificações Técnicas - Auto-Instalador Desktop Multiplataforma Autônomo

## Visão Geral
Este documento contém as especificações técnicas detalhadas para o desenvolvimento do **Auto-Instalador Desktop Multiplataforma Autônomo**, um sistema completamente independente que inclui Docker Engine e Podman Engine embarcados, funcionando sem necessidade de instalações prévias de containerização.

---

## 0. Engines Embarcadas - Sistema Autônomo

### 0.1 Conceito de Autonomia
O Auto-Instalador Desktop é **completamente autônomo**, incluindo binários oficiais das engines de containerização:

- **Docker Engine**: Binários oficiais embarcados para Windows e Linux
- **Podman Engine**: Binários oficiais embarcados para Windows e Linux
- **Detecção Automática**: Identificação de SO, arquitetura e engines existentes
- **Sistema de Fallback**: Escolha inteligente entre engines existentes ou embarcadas
- **Configuração Transparente**: Setup automático de paths, sockets e certificados

### 0.2 Estrutura de Engines Embarcadas
```
engines/
├── docker/
│   ├── windows/
│   │   ├── dockerd.exe              # Docker Daemon para Windows
│   │   ├── docker.exe               # Docker CLI (para compatibilidade)
│   │   └── docker-init.exe          # Docker Init Process
│   └── linux/
│       ├── dockerd                  # Docker Daemon para Linux
│       ├── docker                   # Docker CLI (para compatibilidade)
│       └── docker-init              # Docker Init Process
└── podman/
    ├── windows/
    │   ├── podman.exe               # Podman Engine para Windows
    │   └── conmon.exe               # Container Monitor
    └── linux/
        ├── podman                   # Podman Engine para Linux
        └── conmon                   # Container Monitor
```

### 0.3 Fluxo de Inicialização Autônoma
1. **Detecção de Ambiente**: SO, arquitetura, permissões administrativas
2. **Verificação de Engines**: Busca por Docker/Podman pré-instalados
3. **Apresentação de Opções**: Interface para escolha de engine (existente/embarcada)
4. **Extração Inteligente**: Descompactação de binários apropriados
5. **Configuração Dinâmica**: Setup de diretórios, sockets e configurações
6. **Inicialização Local**: Startup das engines como processos gerenciados
7. **Conexão Nativa**: Estabelecimento de comunicação via APIs .NET
8. **Validação Final**: Testes de conectividade e funcionalidade

---

## 1. Avalonia UI (Versão 11.3.4) - Interface Docker Desktop

### 1.1 Requisitos da Framework
- **Versão Mínima**: 11.3.4
- **Versão Recomendada**: 11.3.4 (última estável)
- **Compatibilidade**: .NET 6.0+ (otimizado para .NET 9)

### 1.2 Pacotes Essenciais
```xml
<PackageReference Include="Avalonia" Version="11.3.4" />
<PackageReference Include="Avalonia.Desktop" Version="11.3.4" />
<PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.4" />
<PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.4" />
<PackageReference Include="Avalonia.ReactiveUI" Version="11.3.4" />
<PackageReference Include="Avalonia.Xaml.Behaviors" Version="11.3.0.6" />
<PackageReference Include="Avalonia.Controls.DataGrid" Version="11.3.4" />
<PackageReference Include="Avalonia.Svg.Skia" Version="11.3.0" />
```

### 1.3 Configurações de Projeto
```xml
<PropertyGroup>
  <OutputType>WinExe</OutputType>
  <TargetFramework>net9.0</TargetFramework>
  <Nullable>enable</Nullable>
  <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
  <ApplicationManifest>app.manifest</ApplicationManifest>
  <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
</PropertyGroup>
```

### 1.4 Recursos Visuais - Tema Docker Desktop
- **Tema Base**: Dark/Blue seguindo exatamente o padrão Docker Desktop
- **Paleta de Cores**:
  - **Background Principal**: #1e1e1e (cinza escuro)
  - **Background Secundário**: #2d2d30 (cinza médio)
  - **Accent Color**: #0078d4 (azul Docker)
  - **Text Primary**: #ffffff (branco)
  - **Text Secondary**: #cccccc (cinza claro)
  - **Success**: #16c60c (verde)
  - **Warning**: #ffb900 (amarelo)
  - **Error**: #e74856 (vermelho)
- **Fontes**: Inter (padrão), Segoe UI (fallback Windows), Ubuntu (fallback Linux)
- **Ícones**: Material Design Icons via Avalonia.Svg com estilo Docker
- **Animações**: Transições suaves de 200-300ms para hover e focus
- **Cards**: Bordas arredondadas (8px) com sombras sutis
- **Botões**: Bordas arredondadas (4px) com estados hover/pressed

### 1.5 Estrutura de Estilos
```
Styles/
├── Colors.axaml          # Paleta de cores do tema
├── Typography.axaml      # Definições tipográficas
├── Controls/
│   ├── Button.axaml      # Estilos de botões
│   ├── TextBox.axaml     # Estilos de campos de texto
│   ├── DataGrid.axaml    # Estilos de tabelas
│   └── Modal.axaml       # Estilos de modais
└── Animations.axaml      # Definições de animações
```

---

## 2. Docker.DotNet (Versão 3.125.15) - Engine Embarcada

### 2.1 Especificações da Biblioteca
- **Versão Mínima**: 3.125.15
- **Versão Recomendada**: 3.125.15 (última estável)
- **Compatibilidade**: Docker Engine API 1.51+ (embarcada e existente)
- **Comunicação**: NUNCA usar Docker CLI - apenas APIs nativas

### 2.2 Configuração de Dependências
```xml
<PackageReference Include="Docker.DotNet" Version="3.125.15" />
<PackageReference Include="Docker.DotNet.X509" Version="3.125.15" />
```

### 2.3 Configuração para Engine Embarcada
```csharp
public class EmbeddedDockerClientConfiguration
{
    public static DockerClient CreateEmbeddedClient(string enginePath)
    {
        // Configuração para Docker Engine embarcada
        var socketPath = Path.Combine(enginePath, "docker.sock");

        var config = new DockerClientConfiguration(
            Environment.OSVersion.Platform == PlatformID.Win32NT
                ? new Uri($"npipe://./pipe/docker_embedded_{Process.GetCurrentProcess().Id}")
                : new Uri($"unix://{socketPath}")
        )
        {
            DefaultTimeout = TimeSpan.FromMinutes(5)
        };

        return config.CreateClient();
    }

    public static DockerClient CreateFallbackClient()
    {
        // Configuração para Docker existente no sistema
        var config = new DockerClientConfiguration(
            Environment.OSVersion.Platform == PlatformID.Win32NT
                ? new Uri("npipe://./pipe/docker_engine")
                : new Uri("unix:///var/run/docker.sock")
        )
        {
            DefaultTimeout = TimeSpan.FromMinutes(5)
        };

        return config.CreateClient();
    }
}
```

### 2.4 Operações Suportadas
- **Gerenciamento de Containers**:
  - Criar, iniciar, parar, remover containers
  - Monitoramento de status e logs
  - Execução de comandos em containers

- **Gerenciamento de Imagens**:
  - Pull/Push de imagens
  - Listagem e remoção de imagens
  - Build de imagens a partir de Dockerfile

- **Gerenciamento de Redes**:
  - Criação e configuração de redes Docker
  - Conexão de containers a redes

- **Gerenciamento de Volumes**:
  - Criação e montagem de volumes
  - Backup e restore de dados

### 2.5 Tratamento de Erros
```csharp
public async Task<bool> IsDockerAvailableAsync()
{
    try
    {
        using var client = DockerClientConfiguration.CreateClient();
        await client.System.PingAsync();
        return true;
    }
    catch (DockerApiException ex)
    {
        // Log específico para erros da API Docker
        return false;
    }
    catch (Exception ex)
    {
        // Log para outros erros (Docker não instalado, etc.)
        return false;
    }
}
```

---

## 3. PodManClient.DotNet (Versão 1.0.4) - Engine Embarcada

### 3.1 Especificações da Biblioteca
- **Versão Mínima**: 1.0.4
- **Versão Recomendada**: 1.0.4 (última estável)
- **Compatibilidade**: Podman Engine API 4.0+ (embarcada e existente)
- **Comunicação**: NUNCA usar Podman CLI - apenas APIs nativas

### 3.2 Configuração de Dependências
```xml
<PackageReference Include="PodManClient.DotNet" Version="1.0.4" />
```

### 3.3 Configuração para Engine Embarcada
```csharp
public class EmbeddedPodmanClientConfiguration
{
    public static PodmanClient CreateEmbeddedClient(string enginePath)
    {
        // Configuração para Podman Engine embarcada
        var socketPath = Path.Combine(enginePath, "podman.sock");

        var config = new PodmanClientConfiguration(
            Environment.OSVersion.Platform == PlatformID.Win32NT
                ? new Uri($"npipe://./pipe/podman_embedded_{Process.GetCurrentProcess().Id}")
                : new Uri($"unix://{socketPath}")
        )
        {
            DefaultTimeout = TimeSpan.FromMinutes(5)
        };

        return config.CreateClient();
    }

    public static PodmanClient CreateFallbackClient()
    {
        // Configuração para Podman existente no sistema
        var config = new PodmanClientConfiguration(
            Environment.OSVersion.Platform == PlatformID.Win32NT
                ? new Uri("npipe://./pipe/podman")
                : new Uri("unix:///run/user/1000/podman/podman.sock")
        )
        {
            DefaultTimeout = TimeSpan.FromMinutes(5)
        };

        return config.CreateClient();
    }
}
```

### 3.4 Diferenças de Implementação
- **Rootless por Padrão**: Podman executa sem privilégios de root
- **Pods Nativos**: Suporte a agrupamento de containers em pods
- **Compatibilidade Docker**: API compatível com Docker para facilitar migração

### 3.5 Configurações Específicas
```csharp
public class PodmanSettings
{
    public bool UseRootless { get; set; } = true;
    public string MachineProfile { get; set; } = "default";
    public bool EnablePodSupport { get; set; } = true;
    public string DefaultRegistry { get; set; } = "docker.io";
}
```

---

## 4. .NET 9 (LTS)

### 4.1 Requisitos da Plataforma
- **Versão**: .NET 9.0.7 (Long Term Support)
- **Runtime**: Microsoft.NETCore.App 9.0.7
- **SDK**: 9.0.303 (para desenvolvimento)

### 4.2 Features Utilizadas

#### 4.2.1 Performance
- **AOT (Ahead-of-Time) Compilation**: Para reduzir tempo de inicialização
- **Trimming**: Remoção de código não utilizado
- **ReadyToRun**: Imagens pré-compiladas para melhor performance

#### 4.2.2 Linguagem (C# 13)
- **Primary Constructors**: Para classes de configuração
- **Collection Expressions**: Sintaxe simplificada para coleções
- **Required Members**: Propriedades obrigatórias em DTOs
- **File-scoped Types**: Para helpers internos

#### 4.2.3 APIs e Bibliotecas
- **System.Text.Json**: Serialização JSON de alta performance
- **Microsoft.Extensions.Hosting**: Host genérico para serviços
- **Microsoft.Extensions.DependencyInjection**: Injeção de dependência
- **Microsoft.Extensions.Configuration**: Sistema de configuração
- **Microsoft.Extensions.Logging**: Sistema de logging estruturado

### 4.3 Configuração de Projeto
```xml
<PropertyGroup>
  <TargetFramework>net9.0.7</TargetFramework>
  <LangVersion>13.0</LangVersion>
  <Nullable>enable</Nullable>
  <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  <EnableNETAnalyzers>true</EnableNETAnalyzers>
  <AnalysisLevel>latest</AnalysisLevel>
  <PublishAot>false</PublishAot>
  <PublishTrimmed>true</PublishTrimmed>
  <PublishReadyToRun>true</PublishReadyToRun>
</PropertyGroup>
```

### 4.4 Pacotes de Extensão
```xml
<PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.0" />
<PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.0" />
<PackageReference Include="System.Text.Json" Version="9.0.0" />
```

### 4.5 Configuração de Deployment
```xml
<!-- Para Windows -->
<RuntimeIdentifier>win-x64</RuntimeIdentifier>
<SelfContained>true</SelfContained>

<!-- Para Linux -->
<RuntimeIdentifier>linux-x64</RuntimeIdentifier>
<SelfContained>true</SelfContained>

<!-- Para macOS -->
<RuntimeIdentifier>osx-x64</RuntimeIdentifier>
<SelfContained>true</SelfContained>
```

---

## 5. Requisitos de Sistema - Auto-Instalador Autônomo

### 5.1 Requisitos Mínimos (Sistema Autônomo)
- **SO**: Windows 10 1903+, Windows 11, Ubuntu 20.04+, Debian 11+
- **RAM**: 4 GB mínimo (8 GB recomendado para melhor performance)
- **Armazenamento**: 2 GB livres para engines embarcadas e dados
- **Arquitetura**: x64 (AMD64) - suporte inicial
- **Permissões**: Usuário com privilégios administrativos para inicialização de engines

### 5.2 Dependências Externas (OPCIONAIS)
- **Docker Desktop/Engine**: Opcional - sistema detecta e oferece como alternativa
- **Podman**: Opcional - sistema detecta e oferece como alternativa
- **.NET 9 Runtime**: Incluído no instalador (self-contained deployment)

### 5.3 Permissões Necessárias para Engines Embarcadas
- **Windows**:
  - Permissões de administrador para inicialização de processos
  - Acesso para criação de named pipes locais
  - Permissões de firewall para comunicação local
- **Linux**:
  - Permissões para criação de sockets Unix locais
  - Acesso para execução de binários em diretório temporário
  - Permissões para gerenciamento de processos filhos

### 5.4 Detecção Automática de Ambiente
- **Sistema Operacional**: Windows/Linux com detecção de distribuição
- **Arquitetura**: x64/ARM64 (preparação futura)
- **Engines Existentes**: Docker Desktop, Docker Engine, Podman
- **Permissões**: Verificação de privilégios administrativos
- **Recursos**: RAM disponível, espaço em disco, CPU

---

## 6. Considerações de Segurança

### 6.1 Comunicação com Container Engines
- Validação de certificados TLS quando aplicável
- Timeout configurável para operações
- Sanitização de inputs do usuário

### 6.2 Gerenciamento de Credenciais
- Armazenamento seguro de credenciais de registry
- Criptografia de dados sensíveis
- Não exposição de secrets em logs

### 6.3 Validação de Imagens
- Verificação de assinaturas quando disponível
- Scan de vulnerabilidades (integração futura)
- Whitelist de registries confiáveis

---

## 7. Monitoramento e Logging - Sistema Autônomo

### 7.1 Estrutura de Logs para Engines Embarcadas
```csharp
public static class LogCategories
{
    // Engines Embarcadas
    public const string DockerEmbedded = "AutoInstaller.Docker.Embedded";
    public const string PodmanEmbedded = "AutoInstaller.Podman.Embedded";
    public const string EngineManager = "AutoInstaller.EngineManager";
    public const string FallbackSystem = "AutoInstaller.FallbackSystem";

    // Sistema Principal
    public const string UI = "AutoInstaller.UI";
    public const string Application = "AutoInstaller.Application";
    public const string Infrastructure = "AutoInstaller.Infrastructure";

    // Agentes Especializados
    public const string AgentDocker = "AutoInstaller.Agent.Docker";
    public const string AgentPodman = "AutoInstaller.Agent.Podman";
    public const string AgentUI = "AutoInstaller.Agent.UI";
    public const string AgentArchitecture = "AutoInstaller.Agent.Architecture";
    public const string AgentInfrastructure = "AutoInstaller.Agent.Infrastructure";
    public const string AgentTests = "AutoInstaller.Agent.Tests";
    public const string AgentDeployment = "AutoInstaller.Agent.Deployment";
}
```

### 7.2 Logs em Tempo Real na Interface
- **Painel Integrado**: Logs visíveis na interface estilo Docker Desktop
- **Filtros por Categoria**: Separação por engine, agente ou componente
- **Níveis Visuais**: Cores diferentes para cada nível de log
- **Busca em Tempo Real**: Filtro textual nos logs exibidos
- **Export de Logs**: Funcionalidade para salvar logs em arquivo

### 7.2 Níveis de Log
- **Trace**: Operações detalhadas de debugging
- **Debug**: Informações de desenvolvimento
- **Information**: Operações normais do sistema
- **Warning**: Situações que requerem atenção
- **Error**: Erros que não impedem a execução
- **Critical**: Erros que impedem a execução

---

*Documento gerado em: Janeiro 2025*
*Versão: 1.0*
*Autor: Arquiteto de Software - Auto-Instalador*